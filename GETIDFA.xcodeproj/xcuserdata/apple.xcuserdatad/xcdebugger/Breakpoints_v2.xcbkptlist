<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "6D6127FE-9A57-425E-A660-E85FBE92E185"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "55938473-540E-489B-9ED1-C52A5155DBDE"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>

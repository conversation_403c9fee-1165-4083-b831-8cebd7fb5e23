//
//  MainViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "MainViewController.h"
#import "TrackingStatusManager.h"
#import "IDFANotDeterminedViewController.h"
#import "IDFAAuthorizedViewController.h"
#import "IDFADeniedGlobalOffViewController.h"
#import "IDFADeniedAppOffViewController.h"
#import "IDFARestrictedViewController.h"
#import "IDFAUnavailableViewController.h"
#import "Masonry.h"

@interface MainViewController ()
@property (nonatomic, strong) UIViewController *currentStateViewController;
@property (nonatomic, strong) IDFABottomToolbar *bottomToolbar;
@property (nonatomic, assign) BOOL hasShownWelcome;
@end

@implementation MainViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    // 设置底部工具栏
    [self setupBottomToolbar];

    [[NSNotificationCenter defaultCenter]
       addObserverForName:UIApplicationDidBecomeActiveNotification
                   object:nil
                    queue:[NSOperationQueue mainQueue]
     usingBlock:^(NSNotification *notification) {
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    // 检查是否需要显示欢迎弹窗
    [self checkAndShowWelcomeIfNeeded];
}

// 统一处理状态并显示对应的界面
- (void)handleStatus:(TrackingStatus)status {
    // 移除当前状态视图控制器
    if (self.currentStateViewController) {
        [self.currentStateViewController.view removeFromSuperview];
        [self.currentStateViewController removeFromParentViewController];
        self.currentStateViewController = nil;
    }

    UIViewController *newViewController = nil;

    switch (status) {
        case TrackingStatusNotDetermined: {
            IDFANotDeterminedViewController *vc = [[IDFANotDeterminedViewController alloc] init];
            vc.onStatusChanged = ^(TrackingStatus newStatus) {
                [self handleStatus:newStatus];
            };
            newViewController = vc;
        } break;
        case TrackingStatusAuthorized: {
            newViewController = [[IDFAAuthorizedViewController alloc] init];
        } break;
        case TrackingStatusDeniedGlobalOff: {
            newViewController = [[IDFADeniedGlobalOffViewController alloc] init];
        } break;
        case TrackingStatusDeniedAppOff: {
            newViewController = [[IDFADeniedAppOffViewController alloc] init];
        } break;
        case TrackingStatusRestricted: {
            newViewController = [[IDFARestrictedViewController alloc] init];
        } break;
        case TrackingStatusUnavailable: {
            newViewController = [[IDFAUnavailableViewController alloc] init];
        } break;
    }

    if (newViewController) {
        [self addChildViewController:newViewController];
        [self.view insertSubview:newViewController.view belowSubview:self.bottomToolbar];
        [newViewController.view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.equalTo(self.view);
            make.bottom.equalTo(self.bottomToolbar.mas_top);
        }];
        [newViewController didMoveToParentViewController:self];
        self.currentStateViewController = newViewController;
    }
}

- (void)setupBottomToolbar {
    self.bottomToolbar = [[IDFABottomToolbar alloc] init];
    self.bottomToolbar.delegate = self;
    [self.view addSubview:self.bottomToolbar];

    [self.bottomToolbar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
        make.height.equalTo(@50);
    }];
}

- (void)checkAndShowWelcomeIfNeeded {
    // 检查是否是第一次启动
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    BOOL hasShownWelcome = [defaults boolForKey:@"HasShownWelcome"];

    if (!hasShownWelcome) {
        // 延迟一点时间确保视图完全加载
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showWelcomeDialog];
        });
    }
    
    else {
        // 如果已经显示过欢迎页，直接初始化界面
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }
}

- (void)showWelcomeDialog {
    IDFAWelcomeViewController *welcomeVC = [[IDFAWelcomeViewController alloc] init];
    welcomeVC.delegate = self;
    welcomeVC.modalPresentationStyle = UIModalPresentationOverFullScreen;
    [self presentViewController:welcomeVC animated:YES completion:nil];
}

#pragma mark - IDFABottomToolbarDelegate

- (void)bottomToolbarDidTapAboutUs {
    IDFADocumentViewController *documentVC = [[IDFADocumentViewController alloc] initWithDocumentType:IDFADocumentTypeAbout];
    documentVC.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:documentVC animated:YES completion:nil];
}

- (void)bottomToolbarDidTapUserAgreement {
    IDFADocumentViewController *documentVC = [[IDFADocumentViewController alloc] initWithDocumentType:IDFADocumentTypeUserAgreement];
    documentVC.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:documentVC animated:YES completion:nil];
}

- (void)bottomToolbarDidTapPrivacyPolicy {
    IDFADocumentViewController *documentVC = [[IDFADocumentViewController alloc] initWithDocumentType:IDFADocumentTypePrivacyPolicy];
    documentVC.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:documentVC animated:YES completion:nil];
}

#pragma mark - IDFAWelcomeViewControllerDelegate

- (void)welcomeViewControllerDidAgree {
    // 用户同意，保存状态并关闭弹窗
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setBool:YES forKey:@"HasShownWelcome"];
    [defaults synchronize];

    [self dismissViewControllerAnimated:YES completion:^{
        // 关闭弹窗后初始化界面
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }];
}

- (void)welcomeViewControllerDidDisagree {
    // 用户不同意，退出应用
    exit(0);
}

- (void)welcomeViewControllerDidTapUserAgreement {
    // 显示用户协议
    [self bottomToolbarDidTapUserAgreement];
}

- (void)welcomeViewControllerDidTapPrivacyPolicy {
    // 显示隐私政策
    [self bottomToolbarDidTapPrivacyPolicy];
}

@end

//
//  MainViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "MainViewController.h"
#import "TrackingStatusManager.h"

@interface MainViewController ()

@end

@implementation MainViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [[NSNotificationCenter defaultCenter]
       addObserverForName:UIApplicationDidBecomeActiveNotification
                   object:nil
                    queue:[NSOperationQueue mainQueue]
     usingBlock:^(NSNotification *notification) {
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }];
}

// 统一处理状态并在需要时调用 requestOrCheckTracking
- (void)handleStatus:(TrackingStatus)status {
    switch (status) {
        case TrackingStatusNotDetermined: {
            // 尚未请求过，可弹窗请求
            // 界面展示：这里界面引导用户点击系统弹窗中的允许按钮，界面上应该有个去授权按钮然后点击按钮调用下面方法弹出系统授权弹窗
            // 界面展示：界面应该有关于IDFA的解释和作用
            // 界面展示：文字提示加图片 guide_notdetermined
            
            [[TrackingStatusManager sharedManager] requestOrCheckTrackingWithCompletion:^(TrackingStatus newStatus) {
                [self handleStatus:newStatus];
            }];
        } break;
        case TrackingStatusAuthorized: {
            // 已授权：可获取 IDFA
            NSUUID *uuid = [ASIdentifierManager sharedManager].advertisingIdentifier;
            NSString *idfa = [uuid UUIDString];
            
            // 界面展示：这里已经获取到idfa，界面展示idfa，可以复制也可以分享
            // 界面展示：界面应该有关于IDFA的解释和作用
            
        } break;
        case TrackingStatusDeniedGlobalOff: {
            // 从未请求过即已 Denied，推测全局开关关闭
            // 全局开关已关闭，无法弹出请求。请先在系统设置里允许“允许应用请求跟踪
            // 界面展示：引导用户 【设置 > 隐私安全 > 跟踪】开启【允许App请求跟踪】
            // 界面展示：步骤对应的引导图片 guide_globaloff_01 > guide_globaloff_02 > guide_globaloff_03
        } break;
        case TrackingStatusDeniedAppOff: {
            // 曾请求过且 Denied，推测用户拒绝，需去本 App 设置页开启
            // 点击【前往开启】或打开【设置 > App > IDFA】开启【允许跟踪】
            //您已拒绝跟踪权限。如需启用，请前往本应用设置页打开跟踪权限。\n点击【前往开启】或打开【设置 > App > IDFA】开启【允许跟踪】
            // 界面展示：这里展示按钮可以直接到达App的设置界面操作允许跟踪
            // 界面展示：也有自行前往开启的步骤引导
            // guide_AppOff_01 > guide_AppOff_02 > guide_AppOff_03
            
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        } break;
        case TrackingStatusRestricted: {
            // 跟踪受限，无法使用此功能。
            // 界面展示：跟踪受限，无法使用此功能。
        } break;
        case TrackingStatusUnavailable: {
            // 当前设备或系统版本不支持跟踪功能。
            // 界面展示：当前设备或系统版本不支持跟踪功能。
        } break;
    }
}



@end

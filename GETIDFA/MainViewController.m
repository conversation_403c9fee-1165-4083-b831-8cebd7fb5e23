//
//  MainViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "MainViewController.h"
#import "TrackingStatusManager.h"
#import "IDFANotDeterminedViewController.h"
#import "IDFAAuthorizedViewController.h"
#import "IDFADeniedGlobalOffViewController.h"
#import "IDFADeniedAppOffViewController.h"
#import "IDFARestrictedViewController.h"
#import "IDFAUnavailableViewController.h"
#import "Masonry.h"

@interface MainViewController ()
@property (nonatomic, strong) UIViewController *currentStateViewController;
@end

@implementation MainViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    // 初始化界面
    [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];

    [[NSNotificationCenter defaultCenter]
       addObserverForName:UIApplicationDidBecomeActiveNotification
                   object:nil
                    queue:[NSOperationQueue mainQueue]
     usingBlock:^(NSNotification *notification) {
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }];
}

// 统一处理状态并显示对应的界面
- (void)handleStatus:(TrackingStatus)status {
    // 移除当前状态视图控制器
    if (self.currentStateViewController) {
        [self.currentStateViewController.view removeFromSuperview];
        [self.currentStateViewController removeFromParentViewController];
        self.currentStateViewController = nil;
    }

    UIViewController *newViewController = nil;

    switch (status) {
        case TrackingStatusNotDetermined: {
            IDFANotDeterminedViewController *vc = [[IDFANotDeterminedViewController alloc] init];
            vc.onStatusChanged = ^(TrackingStatus newStatus) {
                [self handleStatus:newStatus];
            };
            newViewController = vc;
        } break;
        case TrackingStatusAuthorized: {
            newViewController = [[IDFAAuthorizedViewController alloc] init];
        } break;
        case TrackingStatusDeniedGlobalOff: {
            newViewController = [[IDFADeniedGlobalOffViewController alloc] init];
        } break;
        case TrackingStatusDeniedAppOff: {
            newViewController = [[IDFADeniedAppOffViewController alloc] init];
        } break;
        case TrackingStatusRestricted: {
            newViewController = [[IDFARestrictedViewController alloc] init];
        } break;
        case TrackingStatusUnavailable: {
            newViewController = [[IDFAUnavailableViewController alloc] init];
        } break;
    }

    if (newViewController) {
        [self addChildViewController:newViewController];
        [self.view addSubview:newViewController.view];
        [newViewController.view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.view);
        }];
        [newViewController didMoveToParentViewController:self];
        self.currentStateViewController = newViewController;
    }
}

@end
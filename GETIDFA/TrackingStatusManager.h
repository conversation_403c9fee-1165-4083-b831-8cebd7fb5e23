//
//  TrackingStatusManager.h
//  GETIDFA
//
//  Created by apple on 2025/6/12.
//

#import <Foundation/Foundation.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <AdSupport/AdSupport.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, TrackingStatus) {
    TrackingStatusAuthorized,       // 已授权
    TrackingStatusDeniedGlobalOff,  // Denied 且从未请求过：推测全局开关关闭
    TrackingStatusDeniedAppOff,     // Denied 且曾请求过：推测用户曾拒绝
    TrackingStatusRestricted,       // 受限
    TrackingStatusNotDetermined,    // 未决定，可弹窗请求
    TrackingStatusUnavailable       // 不支持或未知
};

@interface TrackingStatusManager : NSObject

+ (instancetype)sharedManager;

// 检查当前跟踪状态，不会触发系统弹窗
- (TrackingStatus)currentTrackingStatus;

// 在需要时调用此方法，自动根据状态决定是否请求或提示，completion 返回最终状态
- (void)requestOrCheckTrackingWithCompletion:(void(^)(TrackingStatus status))completion;

@end

NS_ASSUME_NONNULL_END

//
//  IDFAUnavailableViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAUnavailableViewController.h"

@interface IDFAUnavailableViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *explanationLabel;
@property (nonatomic, strong) UIImageView *iconImageView;
@end

@implementation IDFAUnavailableViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // 图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.image = [UIImage systemImageNamed:@"exclamationmark.triangle"];
    self.iconImageView.tintColor = [UIColor colorWithRed:0.7 green:0.7 blue:0.7 alpha:1.0];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(50);
        make.centerX.equalTo(self.contentView);
        make.width.height.equalTo(@100);
    }];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"⚠️ 功能不可用"];
    self.statusLabel.textColor = [UIColor colorWithRed:0.7 green:0.7 blue:0.7 alpha:1.0];
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView.mas_bottom).offset(40);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
    }];

    // 说明文字
    NSString *explanationText = @"当前设备或系统版本不支持跟踪功能。\n\n可能的原因：\n\n• iOS版本低于14.0\n• 设备不支持IDFA\n• 系统功能异常\n\n建议：\n• 升级到iOS 14.0或更高版本\n• 检查设备兼容性\n• 重启设备后重试";
    self.explanationLabel = [self createDescriptionLabelWithText:explanationText];
    [self.contentView addSubview:self.explanationLabel];
    [self.explanationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(40);
        make.left.equalTo(self.contentView).offset(25);
        make.right.equalTo(self.contentView).offset(-25);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
}

@end

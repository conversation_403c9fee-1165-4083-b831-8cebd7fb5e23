//
//  IDFAThemeManager.h
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, IDFAThemeType) {
    IDFAThemeTypeLight = 0,
    IDFAThemeTypeDark = 1
};

@interface IDFAThemeManager : NSObject

+ (instancetype)sharedManager;

// 主题切换
@property (nonatomic, assign) IDFAThemeType currentTheme;
- (void)switchToTheme:(IDFAThemeType)theme;

// 字体大小
@property (nonatomic, readonly) CGFloat logoFontSize;
@property (nonatomic, readonly) CGFloat titleFontSize;
@property (nonatomic, readonly) CGFloat descriptionFontSize;
@property (nonatomic, readonly) CGFloat buttonFontSize;
@property (nonatomic, readonly) CGFloat stepTitleFontSize;
@property (nonatomic, readonly) CGFloat stepNumberFontSize;
@property (nonatomic, readonly) CGFloat idfaValueFontSize;
@property (nonatomic, readonly) CGFloat hintFontSize;

// 颜色
@property (nonatomic, readonly) UIColor *primaryTextColor;
@property (nonatomic, readonly) UIColor *secondaryTextColor;
@property (nonatomic, readonly) UIColor *successTextColor;
@property (nonatomic, readonly) UIColor *warningTextColor;
@property (nonatomic, readonly) UIColor *errorTextColor;
@property (nonatomic, readonly) UIColor *hintTextColor;
@property (nonatomic, readonly) UIColor *idfaTextColor;

// 背景颜色
@property (nonatomic, readonly) UIColor *containerBackgroundColor;
@property (nonatomic, readonly) UIColor *stepContainerBackgroundColor;
@property (nonatomic, readonly) UIColor *borderColor;

// 渐变色
@property (nonatomic, readonly) NSArray<UIColor *> *gradientColors;
@property (nonatomic, readonly) NSArray<UIColor *> *buttonGradientColors;

// 尺寸
@property (nonatomic, readonly) CGFloat containerCornerRadius;
@property (nonatomic, readonly) CGFloat buttonCornerRadius;
@property (nonatomic, readonly) CGFloat stepContainerCornerRadius;
@property (nonatomic, readonly) CGFloat borderWidth;

// 间距
@property (nonatomic, readonly) CGFloat largeSpacing;
@property (nonatomic, readonly) CGFloat mediumSpacing;
@property (nonatomic, readonly) CGFloat smallSpacing;
@property (nonatomic, readonly) CGFloat horizontalMargin;

// 阴影
@property (nonatomic, readonly) CGSize shadowOffset;
@property (nonatomic, readonly) CGFloat shadowRadius;
@property (nonatomic, readonly) CGFloat shadowOpacity;

@end

NS_ASSUME_NONNULL_END

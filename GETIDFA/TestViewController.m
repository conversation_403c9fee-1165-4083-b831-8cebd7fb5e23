//
//  ViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/12.
//

#import "TestViewController.h"
#import "TrackingStatusManager.h"

@interface TestViewController ()
@property (nonatomic, strong) UIAlertController *currentAlert;

@end

@implementation TestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [[NSNotificationCenter defaultCenter]
       addObserverForName:UIApplicationDidBecomeActiveNotification
                   object:nil
                    queue:[NSOperationQueue mainQueue]
     usingBlock:^(NSNotification *notification) {
        [self handleStatus:[TrackingStatusManager sharedManager].currentTrackingStatus];
    }];
}

// 统一处理状态并在需要时调用 requestOrCheckTracking
- (void)handleStatus:(TrackingStatus)status {
    switch (status) {
        case TrackingStatusNotDetermined: {
            // 尚未请求过，可弹窗请求
            // 可先自定义提示解释，再调用：
            [self showPreRequestAlertWithCompletion:^{
                [[TrackingStatusManager sharedManager] requestOrCheckTrackingWithCompletion:^(TrackingStatus newStatus) {
                    [self handleStatus:newStatus];
                }];
            }];
        } break;
        case TrackingStatusAuthorized: {
            // 已授权：可获取 IDFA
            NSUUID *uuid = [ASIdentifierManager sharedManager].advertisingIdentifier;
            NSString *idfa = [uuid UUIDString];
            [self showMessage:[NSString stringWithFormat:@"已授权，IDFA: %@", idfa]];
        } break;
        case TrackingStatusDeniedGlobalOff: {
            // 从未请求过即已 Denied，推测全局开关关闭
            // 【设置 > 隐私安全 > 跟踪 > 跟踪】开启【允许App请求跟踪】
            [self showSettingsAlertWithMessage:@"系统“隐私 > 跟踪”全局开关已关闭，无法弹出请求。请先在系统设置里允许“允许应用请求跟踪”。\n【设置 > 隐私安全 > 跟踪 > 跟踪】开启【允许App请求跟踪】"];
        } break;
        case TrackingStatusDeniedAppOff: {
            // 曾请求过且 Denied，推测用户拒绝，需去本 App 设置页开启
            // 点击【前往开启】或打开【设置 > App > IDFA】开启【允许跟踪】
            [self showSettingsAlertWithMessage:@"您已拒绝跟踪权限。如需启用，请前往本应用设置页打开跟踪权限。\n点击【前往开启】或打开【设置 > App > IDFA】开启【允许跟踪】"];
        } break;
        case TrackingStatusRestricted: {
            [self showMessage:@"跟踪受限，无法使用此功能。"];
        } break;
        case TrackingStatusUnavailable: {
            [self showMessage:@"当前设备或系统版本不支持跟踪功能。"];
        } break;
    }
}

// 自定义说明弹窗，示例
- (void)showPreRequestAlertWithCompletion:(void(^)(void))completion {
    if (self.currentAlert) {
        [self.currentAlert dismissViewControllerAnimated:YES completion:nil];
    }
    self.currentAlert = [UIAlertController alertControllerWithTitle:@"请求跟踪权限"
                                   message:@"我们希望获取跟踪权限，以便提供更精准的服务，是否继续？"
                            preferredStyle:UIAlertControllerStyleAlert];
    [self.currentAlert addAction:[UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil]];
    [self.currentAlert addAction:[UIAlertAction actionWithTitle:@"继续" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if (completion) completion();
    }]];
    [self presentViewController:self.currentAlert animated:YES completion:nil];
}

// 通用提示跳转设置或仅展示
- (void)showSettingsAlertWithMessage:(NSString *)message {
    if (self.currentAlert) {
        [self.currentAlert dismissViewControllerAnimated:YES completion:nil];
    }
    self.currentAlert  = [UIAlertController alertControllerWithTitle:@"提示"
                                   message:message
                            preferredStyle:UIAlertControllerStyleAlert];
    [self.currentAlert  addAction:[UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil]];
    // 跳转到本 App 设置页
    [self.currentAlert  addAction:[UIAlertAction actionWithTitle:@"前往设置" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    }]];
    [self presentViewController:self.currentAlert  animated:YES completion:nil];
}

// 简单弹出信息
- (void)showMessage:(NSString *)msg {
    if (self.currentAlert) {
        [self.currentAlert dismissViewControllerAnimated:YES completion:nil];
    }
    self.currentAlert = [UIAlertController alertControllerWithTitle:nil
                                   message:msg
                            preferredStyle:UIAlertControllerStyleAlert];
    [self.currentAlert addAction:[UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:nil]];
    [self presentViewController:self.currentAlert animated:YES completion:nil];
}
@end

//
//  IDFAGradientView.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientView.h"

@interface IDFAGradientView ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIVisualEffectView *blurEffectView;
@end

@implementation IDFAGradientView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        NSLog(@"IDFAGradientView initWithFrame: %@", NSStringFromCGRect(frame));
        [self setupGradientBackground];
        [self addGlassEffect];
        // 延迟启动动画，确保视图已经布局完成
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self addEnhancedAnimations];
        });
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.gradientLayer.frame = self.bounds;
    self.blurEffectView.frame = self.bounds;
}

- (void)setupGradientBackground {
    // 创建现代蓝紫渐变背景
    self.gradientLayer = [CAGradientLayer layer];
    
    // 现代蓝色紫色渐变色彩
    UIColor *topColor = [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0];      // 现代蓝色
    UIColor *middleColor = [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0];   // 蓝紫色
    UIColor *bottomColor = [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0];   // 紫色
    
    self.gradientLayer.colors = @[
        (id)topColor.CGColor,
        (id)middleColor.CGColor,
        (id)bottomColor.CGColor
    ];
    
    // 设置渐变位置和方向
    self.gradientLayer.locations = @[@0.0, @0.5, @1.0];
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    
    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)addGlassEffect {
    // 添加毛玻璃效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemUltraThinMaterial];
    self.blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    self.blurEffectView.alpha = 0.3; // 轻微的毛玻璃效果
    [self addSubview:self.blurEffectView];
}

- (void)addEnhancedAnimations {
    
    // 0.添加柔和的灯光效果
    [self addSoftLightEffect];
    
    // 1. 渐变色动画
    [self addGradientColorAnimation];

    // 2. 多层柔光效果
    [self addMultiLayerLightEffect];

    // 3. 浮动粒子效果
    [self addFloatingParticles];

    // 4. 波纹效果
    [self addRippleEffect];
}

- (void)addSoftLightEffect {
    // 添加柔光折射效果
    CALayer *lightLayer = [CALayer layer];
    lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1].CGColor;
    lightLayer.cornerRadius = 100;
    lightLayer.frame = CGRectMake(-50, -50, 200, 200);
    
    // 添加阴影效果模拟柔光
    lightLayer.shadowColor = [UIColor whiteColor].CGColor;
    lightLayer.shadowOffset = CGSizeZero;
    lightLayer.shadowRadius = 50;
    lightLayer.shadowOpacity = 0.3;
    
    [self.layer addSublayer:lightLayer];
    
    // 添加动画效果
    CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
    moveAnimation.duration = 8.0;
    moveAnimation.repeatCount = INFINITY;
    moveAnimation.autoreverses = YES;
    moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(100, 100)];
    moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(300, 300)];
    
    [lightLayer addAnimation:moveAnimation forKey:@"softLightMove"];
}

- (void)addGradientColorAnimation {
    // 渐变背景颜色缓慢变化
    CABasicAnimation *colorAnimation = [CABasicAnimation animationWithKeyPath:@"colors"];
    colorAnimation.duration = 12.0;
    colorAnimation.repeatCount = INFINITY;
    colorAnimation.autoreverses = YES;

    // 原始颜色组合
    UIColor *topColor1 = [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0];
    UIColor *middleColor1 = [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0];
    UIColor *bottomColor1 = [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0];

    // 变化后的颜色组合
    UIColor *topColor2 = [UIColor colorWithRed:0.3 green:0.7 blue:0.9 alpha:1.0];
    UIColor *middleColor2 = [UIColor colorWithRed:0.5 green:0.5 blue:1.0 alpha:1.0];
    UIColor *bottomColor2 = [UIColor colorWithRed:0.7 green:0.4 blue:0.8 alpha:1.0];

    colorAnimation.fromValue = @[
        (id)topColor1.CGColor,
        (id)middleColor1.CGColor,
        (id)bottomColor1.CGColor
    ];

    colorAnimation.toValue = @[
        (id)topColor2.CGColor,
        (id)middleColor2.CGColor,
        (id)bottomColor2.CGColor
    ];

    [self.gradientLayer addAnimation:colorAnimation forKey:@"gradientColorAnimation"];
}

- (void)addMultiLayerLightEffect {
    // 创建3个不同大小的柔光层
    for (int i = 0; i < 3; i++) {
        CALayer *lightLayer = [CALayer layer];
        lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.06 + i * 0.02].CGColor;
        lightLayer.cornerRadius = 60 + (i * 30);

        // 添加发光效果
        lightLayer.shadowColor = [UIColor whiteColor].CGColor;
        lightLayer.shadowOffset = CGSizeZero;
        lightLayer.shadowRadius = 30 + (i * 15);
        lightLayer.shadowOpacity = 0.2 + (i * 0.05);

        [self.layer addSublayer:lightLayer];

        // 移动动画
        CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
        moveAnimation.duration = 8.0 + (i * 3.0);
        moveAnimation.repeatCount = INFINITY;
        moveAnimation.autoreverses = YES;
        moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(50 + i * 80, 100 + i * 120)];
        moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(300 + i * 50, 500 + i * 80)];

        // 缩放动画
        CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        scaleAnimation.duration = 6.0 + (i * 2.0);
        scaleAnimation.repeatCount = INFINITY;
        scaleAnimation.autoreverses = YES;
        scaleAnimation.fromValue = @(0.7 + i * 0.1);
        scaleAnimation.toValue = @(1.3 + i * 0.1);

        // 组合动画
        CAAnimationGroup *group = [CAAnimationGroup animation];
        group.animations = @[moveAnimation, scaleAnimation];
        group.duration = moveAnimation.duration;
        group.repeatCount = INFINITY;

        [lightLayer addAnimation:group forKey:[NSString stringWithFormat:@"lightEffect%d", i]];
    }
}

- (void)addFloatingParticles {
    // 创建12个浮动粒子
    for (int i = 0; i < 12; i++) {
        CALayer *particle = [CALayer layer];
        particle.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;

        // 不同大小的粒子
        CGFloat size = 2 + (i % 4);
        particle.cornerRadius = size / 2;
        particle.frame = CGRectMake(0, 0, size, size);

        // 随机初始位置
        particle.position = CGPointMake(arc4random_uniform(350), arc4random_uniform(700));

        [self.layer addSublayer:particle];

        // 垂直浮动动画
        CABasicAnimation *floatAnimation = [CABasicAnimation animationWithKeyPath:@"position.y"];
        floatAnimation.duration = 4.0 + (i * 0.5);
        floatAnimation.repeatCount = INFINITY;
        floatAnimation.autoreverses = YES;
        floatAnimation.fromValue = @(particle.position.y);
        floatAnimation.toValue = @(particle.position.y - 80 - (i * 5));

        // 水平漂移动画
        CABasicAnimation *driftAnimation = [CABasicAnimation animationWithKeyPath:@"position.x"];
        driftAnimation.duration = 6.0 + (i * 0.8);
        driftAnimation.repeatCount = INFINITY;
        driftAnimation.autoreverses = YES;
        driftAnimation.fromValue = @(particle.position.x);
        driftAnimation.toValue = @(particle.position.x + 30 - (i % 3) * 20);

        // 透明度动画
        CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        opacityAnimation.duration = 3.0 + (i * 0.4);
        opacityAnimation.repeatCount = INFINITY;
        opacityAnimation.autoreverses = YES;
        opacityAnimation.fromValue = @(0.1);
        opacityAnimation.toValue = @(0.8);

        // 组合所有动画
        CAAnimationGroup *particleGroup = [CAAnimationGroup animation];
        particleGroup.animations = @[floatAnimation, driftAnimation, opacityAnimation];
        particleGroup.duration = floatAnimation.duration;
        particleGroup.repeatCount = INFINITY;

        [particle addAnimation:particleGroup forKey:[NSString stringWithFormat:@"particle%d", i]];
    }
}

- (void)addRippleEffect {
    // 创建波纹效果
    for (int i = 0; i < 2; i++) {
        CALayer *ripple = [CALayer layer];
        ripple.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
        ripple.borderWidth = 1.0;
        ripple.backgroundColor = [UIColor clearColor].CGColor;
        ripple.cornerRadius = 50;
        ripple.frame = CGRectMake(0, 0, 100, 100);
        ripple.position = CGPointMake(200 + i * 100, 400 + i * 150);

        [self.layer addSublayer:ripple];

        // 扩散动画
        CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        scaleAnimation.duration = 4.0 + i * 2.0;
        scaleAnimation.repeatCount = INFINITY;
        scaleAnimation.fromValue = @(0.5);
        scaleAnimation.toValue = @(2.0);

        // 透明度动画
        CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        opacityAnimation.duration = scaleAnimation.duration;
        opacityAnimation.repeatCount = INFINITY;
        opacityAnimation.fromValue = @(0.8);
        opacityAnimation.toValue = @(0.0);

        // 组合动画
        CAAnimationGroup *rippleGroup = [CAAnimationGroup animation];
        rippleGroup.animations = @[scaleAnimation, opacityAnimation];
        rippleGroup.duration = scaleAnimation.duration;
        rippleGroup.repeatCount = INFINITY;

        [ripple addAnimation:rippleGroup forKey:[NSString stringWithFormat:@"ripple%d", i]];
    }
}

- (void)restartAnimations {
    NSLog(@"重新启动IDFAGradientView动画，当前frame: %@", NSStringFromCGRect(self.frame));

    // 移除所有现有动画
    [self.layer removeAllAnimations];
    for (CALayer *sublayer in self.layer.sublayers) {
        [sublayer removeAllAnimations];
    }

    // 重新启动动画
    [self addEnhancedAnimations];
}

@end

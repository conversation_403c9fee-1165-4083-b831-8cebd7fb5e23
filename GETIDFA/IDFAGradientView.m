//
//  IDFAGradientView.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientView.h"

@interface IDFAGradientView ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIVisualEffectView *blurEffectView;
@end

@implementation IDFAGradientView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupGradientBackground];
        [self addGlassEffect];
        [self addSoftLightEffect];
        [self addFloatingParticles];
        [self addGradientAnimation];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.gradientLayer.frame = self.bounds;
    self.blurEffectView.frame = self.bounds;
}

- (void)setupGradientBackground {
    // 创建现代蓝紫渐变背景
    self.gradientLayer = [CAGradientLayer layer];
    
    // 现代蓝色紫色渐变色彩
    UIColor *topColor = [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0];      // 现代蓝色
    UIColor *middleColor = [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0];   // 蓝紫色
    UIColor *bottomColor = [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0];   // 紫色
    
    self.gradientLayer.colors = @[
        (id)topColor.CGColor,
        (id)middleColor.CGColor,
        (id)bottomColor.CGColor
    ];
    
    // 设置渐变位置和方向
    self.gradientLayer.locations = @[@0.0, @0.5, @1.0];
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    
    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)addGlassEffect {
    // 添加毛玻璃效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemUltraThinMaterial];
    self.blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    self.blurEffectView.alpha = 0.3; // 轻微的毛玻璃效果
    [self addSubview:self.blurEffectView];
}

- (void)addSoftLightEffect {
    // 添加多个柔光折射效果
    for (int i = 0; i < 3; i++) {
        CALayer *lightLayer = [CALayer layer];
        lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.08].CGColor;
        lightLayer.cornerRadius = 80 + (i * 20);

        // 添加阴影效果模拟柔光
        lightLayer.shadowColor = [UIColor whiteColor].CGColor;
        lightLayer.shadowOffset = CGSizeZero;
        lightLayer.shadowRadius = 40 + (i * 10);
        lightLayer.shadowOpacity = 0.2 + (i * 0.05);

        [self.layer addSublayer:lightLayer];

        // 添加移动动画
        CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
        moveAnimation.duration = 6.0 + (i * 2.0);
        moveAnimation.repeatCount = INFINITY;
        moveAnimation.autoreverses = YES;
        moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(50 + i * 100, 50 + i * 80)];
        moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(250 + i * 50, 400 + i * 100)];

        // 添加缩放动画
        CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        scaleAnimation.duration = 4.0 + (i * 1.5);
        scaleAnimation.repeatCount = INFINITY;
        scaleAnimation.autoreverses = YES;
        scaleAnimation.fromValue = @(0.8);
        scaleAnimation.toValue = @(1.2);

        // 组合动画
        CAAnimationGroup *group = [CAAnimationGroup animation];
        group.animations = @[moveAnimation, scaleAnimation];
        group.duration = moveAnimation.duration;
        group.repeatCount = INFINITY;

        [lightLayer addAnimation:group forKey:[NSString stringWithFormat:@"softLightMove%d", i]];
    }
}

- (void)addFloatingParticles {
    // 添加浮动粒子效果
    for (int i = 0; i < 8; i++) {
        CALayer *particle = [CALayer layer];
        particle.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
        particle.cornerRadius = 2 + (i % 3);
        particle.frame = CGRectMake(0, 0, 4 + (i % 3) * 2, 4 + (i % 3) * 2);

        // 随机初始位置
        particle.position = CGPointMake(arc4random_uniform(300), arc4random_uniform(600));

        [self.layer addSublayer:particle];

        // 浮动动画
        CABasicAnimation *floatAnimation = [CABasicAnimation animationWithKeyPath:@"position.y"];
        floatAnimation.duration = 3.0 + (i * 0.5);
        floatAnimation.repeatCount = INFINITY;
        floatAnimation.autoreverses = YES;
        floatAnimation.fromValue = @(particle.position.y);
        floatAnimation.toValue = @(particle.position.y - 50 - (i * 10));

        // 透明度动画
        CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        opacityAnimation.duration = 2.0 + (i * 0.3);
        opacityAnimation.repeatCount = INFINITY;
        opacityAnimation.autoreverses = YES;
        opacityAnimation.fromValue = @(0.2);
        opacityAnimation.toValue = @(0.8);

        // 组合动画
        CAAnimationGroup *particleGroup = [CAAnimationGroup animation];
        particleGroup.animations = @[floatAnimation, opacityAnimation];
        particleGroup.duration = floatAnimation.duration;
        particleGroup.repeatCount = INFINITY;

        [particle addAnimation:particleGroup forKey:[NSString stringWithFormat:@"particle%d", i]];
    }
}

- (void)addGradientAnimation {
    // 添加渐变色动画
    CABasicAnimation *colorAnimation = [CABasicAnimation animationWithKeyPath:@"colors"];
    colorAnimation.duration = 8.0;
    colorAnimation.repeatCount = INFINITY;
    colorAnimation.autoreverses = YES;

    // 原始颜色
    UIColor *topColor1 = [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0];
    UIColor *middleColor1 = [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0];
    UIColor *bottomColor1 = [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0];

    // 变化后的颜色
    UIColor *topColor2 = [UIColor colorWithRed:0.3 green:0.7 blue:0.9 alpha:1.0];
    UIColor *middleColor2 = [UIColor colorWithRed:0.5 green:0.5 blue:1.0 alpha:1.0];
    UIColor *bottomColor2 = [UIColor colorWithRed:0.7 green:0.4 blue:0.8 alpha:1.0];

    colorAnimation.fromValue = @[
        (id)topColor1.CGColor,
        (id)middleColor1.CGColor,
        (id)bottomColor1.CGColor
    ];

    colorAnimation.toValue = @[
        (id)topColor2.CGColor,
        (id)middleColor2.CGColor,
        (id)bottomColor2.CGColor
    ];

    [self.gradientLayer addAnimation:colorAnimation forKey:@"gradientColorAnimation"];
}

@end

//
//  IDFAGradientView.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientView.h"

@interface IDFAGradientView ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIVisualEffectView *blurEffectView;
@end

@implementation IDFAGradientView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupGradientBackground];
        [self addGlassEffect];
        [self addSoftLightEffect];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.gradientLayer.frame = self.bounds;
    self.blurEffectView.frame = self.bounds;
}

- (void)setupGradientBackground {
    // 创建现代蓝紫渐变背景
    self.gradientLayer = [CAGradientLayer layer];
    
    // 现代蓝色紫色渐变色彩
    UIColor *topColor = [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0];      // 现代蓝色
    UIColor *middleColor = [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0];   // 蓝紫色
    UIColor *bottomColor = [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0];   // 紫色
    
    self.gradientLayer.colors = @[
        (id)topColor.CGColor,
        (id)middleColor.CGColor,
        (id)bottomColor.CGColor
    ];
    
    // 设置渐变位置和方向
    self.gradientLayer.locations = @[@0.0, @0.5, @1.0];
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    
    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)addGlassEffect {
    // 添加毛玻璃效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemUltraThinMaterial];
    self.blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    self.blurEffectView.alpha = 0.3; // 轻微的毛玻璃效果
    [self addSubview:self.blurEffectView];
}

- (void)addSoftLightEffect {
    // 添加柔光折射效果
    CALayer *lightLayer = [CALayer layer];
    lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1].CGColor;
    lightLayer.cornerRadius = 100;
    lightLayer.frame = CGRectMake(-50, -50, 200, 200);
    
    // 添加阴影效果模拟柔光
    lightLayer.shadowColor = [UIColor whiteColor].CGColor;
    lightLayer.shadowOffset = CGSizeZero;
    lightLayer.shadowRadius = 50;
    lightLayer.shadowOpacity = 0.3;
    
    [self.layer addSublayer:lightLayer];
    
    // 添加动画效果
    CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
    moveAnimation.duration = 8.0;
    moveAnimation.repeatCount = INFINITY;
    moveAnimation.autoreverses = YES;
    moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(100, 100)];
    moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(300, 300)];
    
    [lightLayer addAnimation:moveAnimation forKey:@"softLightMove"];
}

@end

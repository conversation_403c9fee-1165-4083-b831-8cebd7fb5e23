# IDFAGradientButton 使用指南

## 概述

`IDFAGradientButton` 是一个支持多种尺寸的渐变按钮控件，提供了三种预设尺寸和完整的主题适配功能。

## 按钮尺寸

### 1. 正常尺寸 (IDFAButtonSizeNormal)
- **高度**: 44pt
- **字体**: 主题按钮字体大小
- **圆角**: 主题按钮圆角
- **用途**: 主要操作按钮，如"同意并继续"、"去授权"等

### 2. 小按钮 (IDFAButtonSizeSmall)
- **高度**: 36pt
- **字体**: 主题按钮字体大小 - 2pt
- **圆角**: 主题按钮圆角 - 4pt
- **用途**: 次要操作按钮，如"关闭"、"取消"等

### 3. 迷你按钮 (IDFAButtonSizeMini)
- **高度**: 28pt
- **字体**: 主题按钮字体大小 - 4pt
- **圆角**: 主题按钮圆角 - 8pt
- **用途**: 辅助操作按钮，如工具栏按钮、标签等

## 使用方法

### 基本创建

```objc
// 创建正常尺寸按钮（默认）
IDFAGradientButton *normalButton = [[IDFAGradientButton alloc] initWithTitle:@"确定"];

// 创建指定尺寸按钮
IDFAGradientButton *smallButton = [[IDFAGradientButton alloc] initWithTitle:@"取消" size:IDFAButtonSizeSmall];
IDFAGradientButton *miniButton = [[IDFAGradientButton alloc] initWithTitle:@"更多" size:IDFAButtonSizeMini];
```

### 在基础控制器中使用

```objc
// 通过基础控制器创建
IDFAGradientButton *button1 = [self createGradientButtonWithTitle:@"主要操作"];
IDFAGradientButton *button2 = [self createGradientButtonWithTitle:@"次要操作" size:IDFAButtonSizeSmall];
```

### 获取推荐高度

```objc
// 类方法获取推荐高度
CGFloat normalHeight = [IDFAGradientButton recommendedHeightForSize:IDFAButtonSizeNormal]; // 44.0
CGFloat smallHeight = [IDFAGradientButton recommendedHeightForSize:IDFAButtonSizeSmall];   // 36.0
CGFloat miniHeight = [IDFAGradientButton recommendedHeightForSize:IDFAButtonSizeMini];     // 28.0

// 实例方法获取当前按钮推荐高度
CGFloat height = [button recommendedHeight];
```

### 约束设置示例

```objc
// 使用推荐高度设置约束
[button mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.equalTo(self.view);
    make.top.equalTo(self.view).offset(100);
    make.width.equalTo(@200);
    make.height.equalTo(@([button recommendedHeight]));
}];
```

## 实际应用场景

### 1. 主界面按钮
```objc
// 主要操作 - 使用正常尺寸
self.authorizeButton = [[IDFAGradientButton alloc] initWithTitle:@"🔓 去授权" size:IDFAButtonSizeNormal];
[self.authorizeButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.height.equalTo(@44);
}];
```

### 2. 弹窗按钮
```objc
// 弹窗关闭按钮 - 使用小尺寸
self.closeButton = [[IDFAGradientButton alloc] initWithTitle:@"关闭" size:IDFAButtonSizeSmall];
[self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.height.equalTo(@36);
}];
```

### 3. 工具栏按钮
```objc
// 工具栏按钮 - 使用迷你尺寸
self.toolbarButton = [[IDFAGradientButton alloc] initWithTitle:@"设置" size:IDFAButtonSizeMini];
[self.toolbarButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.height.equalTo(@28);
}];
```

## 主题适配

所有尺寸的按钮都会自动适配当前主题：
- 自动监听主题变化通知
- 动态更新字体大小、颜色、圆角等
- 支持亮色/暗色主题切换

## 特性

- ✅ **多尺寸支持**: 三种预设尺寸满足不同场景需求
- ✅ **主题适配**: 自动跟随主题变化
- ✅ **渐变背景**: 美观的渐变色背景
- ✅ **触摸动画**: 内置按压反馈动画
- ✅ **便利方法**: 提供推荐高度获取方法
- ✅ **一致性**: 保持设计语言的统一性

## 注意事项

1. **高度约束**: 建议使用 `recommendedHeight` 方法获取推荐高度
2. **宽度设置**: 根据内容和设计需求自行设置宽度
3. **主题切换**: 按钮会自动响应主题变化，无需手动更新
4. **内存管理**: 按钮会自动管理通知观察者的注册和注销

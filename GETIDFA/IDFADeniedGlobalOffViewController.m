//
//  IDFADeniedGlobalOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedGlobalOffViewController.h"

@interface IDFADeniedGlobalOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@property (nonatomic, strong) UIStackView *stepsStackView;
@end

@implementation IDFADeniedGlobalOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // 状态标题
    self.statusLabel = [self createInfoLabelWithText:@"⚠️ 系统跟踪权限已关闭" fontSize:24];
    self.statusLabel.textColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(40);
        make.centerX.equalTo(self.contentView);
    }];
    
    // 说明文字
    NSString *instructionText = @"系统\"隐私 > 跟踪\"全局开关已关闭，无法弹出请求。\n请按照以下步骤开启系统跟踪权限：";
    self.instructionLabel = [self createInfoLabelWithText:instructionText fontSize:16];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(30);
        make.right.equalTo(self.contentView).offset(-30);
    }];
    
    // 步骤引导
    self.stepsStackView = [[UIStackView alloc] init];
    self.stepsStackView.axis = UILayoutConstraintAxisVertical;
    self.stepsStackView.spacing = 20;
    self.stepsStackView.alignment = UIStackViewAlignmentFill;
    [self.contentView addSubview:self.stepsStackView];
    [self.stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
    
    // 添加步骤
    [self addStepWithNumber:1 title:@"打开系统设置" imageName:@"guide_globaloff_01"];
    [self addStepWithNumber:2 title:@"进入隐私安全 > 跟踪" imageName:@"guide_globaloff_02"];
    [self addStepWithNumber:3 title:@"开启\"允许App请求跟踪\"" imageName:@"guide_globaloff_03"];
}

- (void)addStepWithNumber:(NSInteger)stepNumber title:(NSString *)title imageName:(NSString *)imageName {
    // 步骤容器
    UIView *stepContainer = [[UIView alloc] init];
    stepContainer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1];
    stepContainer.layer.cornerRadius = 15;
    stepContainer.layer.borderWidth = 1;
    stepContainer.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    [self.stepsStackView addArrangedSubview:stepContainer];
    [stepContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@120);
    }];
    
    // 步骤编号
    UILabel *stepNumberLabel = [[UILabel alloc] init];
    stepNumberLabel.text = [NSString stringWithFormat:@"%ld", (long)stepNumber];
    stepNumberLabel.font = [UIFont boldSystemFontOfSize:20];
    stepNumberLabel.textColor = [UIColor whiteColor];
    stepNumberLabel.textAlignment = NSTextAlignmentCenter;
    stepNumberLabel.backgroundColor = [UIColor colorWithRed:0.3 green:0.5 blue:1.0 alpha:0.8];
    stepNumberLabel.layer.cornerRadius = 15;
    stepNumberLabel.layer.masksToBounds = YES;
    [stepContainer addSubview:stepNumberLabel];
    [stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepContainer).offset(15);
        make.centerY.equalTo(stepContainer);
        make.width.height.equalTo(@30);
    }];
    
    // 步骤标题
    UILabel *stepTitleLabel = [self createInfoLabelWithText:title fontSize:16];
    stepTitleLabel.textAlignment = NSTextAlignmentLeft;
    [stepContainer addSubview:stepTitleLabel];
    [stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepNumberLabel.mas_right).offset(15);
        make.top.equalTo(stepContainer).offset(15);
        make.right.equalTo(stepContainer).offset(-120);
    }];
    
    // 引导图片
    UIImageView *guideImageView = [[UIImageView alloc] init];
    guideImageView.image = [UIImage imageNamed:imageName];
    guideImageView.contentMode = UIViewContentModeScaleAspectFit;
    guideImageView.layer.cornerRadius = 8;
    guideImageView.layer.masksToBounds = YES;
    guideImageView.layer.borderWidth = 1;
    guideImageView.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    [stepContainer addSubview:guideImageView];
    [guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(stepContainer).offset(-15);
        make.centerY.equalTo(stepContainer);
        make.width.equalTo(@90);
        make.height.equalTo(@90);
    }];
}

@end

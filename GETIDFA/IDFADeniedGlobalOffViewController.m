//
//  IDFADeniedGlobalOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedGlobalOffViewController.h"

@interface IDFADeniedGlobalOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@property (nonatomic, strong) UIStackView *stepsStackView;
@end

@implementation IDFADeniedGlobalOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"⚠️ 系统跟踪权限已关闭"];
    self.statusLabel.textColor = theme.warningTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.largeSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    NSString *instructionText = @"系统\"隐私 > 跟踪\"全局开关已关闭，无法弹出请求。\n请按照以下步骤开启系统跟踪权限：";
    self.instructionLabel = [self createDescriptionLabelWithText:instructionText];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];
    
    // 步骤引导
    self.stepsStackView = [[UIStackView alloc] init];
    self.stepsStackView.axis = UILayoutConstraintAxisVertical;
    self.stepsStackView.spacing = theme.mediumSpacing;
    self.stepsStackView.alignment = UIStackViewAlignmentFill;
    [self.contentView addSubview:self.stepsStackView];
    [self.stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.smallSpacing);
        make.right.equalTo(self.contentView).offset(-theme.smallSpacing);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];

    // 添加步骤
    [self addStepWithNumber:1 title:@"打开系统设置" imageName:@"guide_globaloff_01"];
    [self addStepWithNumber:2 title:@"进入隐私安全 > 跟踪" imageName:@"guide_globaloff_02"];
    [self addStepWithNumber:3 title:@"开启\"允许App请求跟踪\"" imageName:@"guide_globaloff_03"];
}

- (void)addStepWithNumber:(NSInteger)stepNumber title:(NSString *)title imageName:(NSString *)imageName {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 步骤容器
    UIView *stepContainer = [[UIView alloc] init];
    stepContainer.backgroundColor = theme.stepContainerBackgroundColor;
    stepContainer.layer.cornerRadius = theme.stepContainerCornerRadius;
    stepContainer.layer.borderWidth = theme.borderWidth;
    stepContainer.layer.borderColor = theme.borderColor.CGColor;

    // 添加阴影
    stepContainer.layer.shadowColor = [UIColor blackColor].CGColor;
    stepContainer.layer.shadowOffset = theme.shadowOffset;
    stepContainer.layer.shadowRadius = theme.shadowRadius;
    stepContainer.layer.shadowOpacity = theme.shadowOpacity;
    stepContainer.layer.masksToBounds = NO;

    [self.stepsStackView addArrangedSubview:stepContainer];
    [stepContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@120);
    }];

    // 步骤编号
    UILabel *stepNumberLabel = [[UILabel alloc] init];
    stepNumberLabel.text = [NSString stringWithFormat:@"%ld", (long)stepNumber];
    stepNumberLabel.font = [UIFont boldSystemFontOfSize:theme.stepNumberFontSize];
    stepNumberLabel.textColor = theme.primaryTextColor;
    stepNumberLabel.textAlignment = NSTextAlignmentCenter;
    NSArray *buttonColors = theme.buttonGradientColors;
    stepNumberLabel.backgroundColor = buttonColors.firstObject;
    stepNumberLabel.layer.cornerRadius = theme.stepContainerCornerRadius;
    stepNumberLabel.layer.masksToBounds = YES;
    [stepContainer addSubview:stepNumberLabel];
    [stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepContainer).offset(theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.width.height.equalTo(@36);
    }];

    // 步骤标题
    UILabel *stepTitleLabel = [self createInfoLabelWithText:title fontSize:theme.stepTitleFontSize];
    stepTitleLabel.textAlignment = NSTextAlignmentLeft;
    stepTitleLabel.font = [UIFont boldSystemFontOfSize:theme.stepTitleFontSize];
    [stepContainer addSubview:stepTitleLabel];
    [stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepNumberLabel.mas_right).offset(theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.right.equalTo(stepContainer).offset(-110);
    }];

    // 引导图片（可点击放大）
    UIImageView *guideImageView = [self createTappableImageViewWithImageName:imageName];
    [stepContainer addSubview:guideImageView];
    [guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(stepContainer).offset(-theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.width.equalTo(@90);
        make.height.equalTo(@90);
    }];
}

@end

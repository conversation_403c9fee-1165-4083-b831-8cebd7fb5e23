//
//  IDFABaseViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFABaseViewController.h"

@implementation IDFABaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupBaseUI];
}

- (void)setupBaseUI {
    // 设置渐变背景
    self.gradientBackgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 设置滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view.mas_safeAreaLayoutGuide);
    }];
    
    // 内容视图
    self.contentView = [[UIView alloc] init];
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    [self setupIDFALogo];
}

- (void)setupIDFALogo {
    // 创建3D立体IDFA标题
    self.idfaLogoLabel = [[UILabel alloc] init];
    self.idfaLogoLabel.text = @"IDFA";
    self.idfaLogoLabel.font = [UIFont boldSystemFontOfSize:48];
    self.idfaLogoLabel.textAlignment = NSTextAlignmentCenter;
    
    // 设置渐变文字颜色
    self.idfaLogoLabel.textColor = [UIColor whiteColor];
    
    // 添加阴影效果模拟3D立体感
    self.idfaLogoLabel.layer.shadowColor = [UIColor blackColor].CGColor;
    self.idfaLogoLabel.layer.shadowOffset = CGSizeMake(2, 2);
    self.idfaLogoLabel.layer.shadowRadius = 4;
    self.idfaLogoLabel.layer.shadowOpacity = 0.5;
    
    [self.contentView addSubview:self.idfaLogoLabel];
    [self.idfaLogoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(60);
        make.centerX.equalTo(self.contentView);
    }];
}

- (UIButton *)createGradientButtonWithTitle:(NSString *)title {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont boldSystemFontOfSize:16];

    // 设置圆角
    button.layer.cornerRadius = 25;

    // 创建渐变背景
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.3 green:0.5 blue:1.0 alpha:0.8].CGColor,
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:0.9 alpha:0.8].CGColor
    ];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 1);
    gradientLayer.cornerRadius = 25;
    gradientLayer.frame = CGRectMake(0, 0, 200, 50); // 设置默认frame，会在layoutSubviews中更新

    [button.layer insertSublayer:gradientLayer atIndex:0];

    // 添加边框效果
    button.layer.borderWidth = 1;
    button.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;

    // 添加阴影
    button.layer.shadowColor = [UIColor blackColor].CGColor;
    button.layer.shadowOffset = CGSizeMake(0, 2);
    button.layer.shadowRadius = 4;
    button.layer.shadowOpacity = 0.3;
    button.layer.masksToBounds = NO;

    return button;
}

- (UILabel *)createInfoLabelWithText:(NSString *)text fontSize:(CGFloat)fontSize {
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont systemFontOfSize:fontSize];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;
    
    // 添加文字阴影
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(1, 1);
    label.layer.shadowRadius = 2;
    label.layer.shadowOpacity = 0.5;
    
    return label;
}

- (void)showCopySuccessMessage {
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = @"已复制到剪贴板";
    messageLabel.font = [UIFont systemFontOfSize:14];
    messageLabel.textColor = [UIColor whiteColor];
    messageLabel.textAlignment = NSTextAlignmentCenter;
    messageLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    messageLabel.layer.cornerRadius = 15;
    messageLabel.layer.masksToBounds = YES;
    messageLabel.alpha = 0;
    
    [self.view addSubview:messageLabel];
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.equalTo(@120);
        make.height.equalTo(@30);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
        messageLabel.alpha = 1;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 delay:1.5 options:0 animations:^{
            messageLabel.alpha = 0;
        } completion:^(BOOL finished) {
            [messageLabel removeFromSuperview];
        }];
    }];
}

@end

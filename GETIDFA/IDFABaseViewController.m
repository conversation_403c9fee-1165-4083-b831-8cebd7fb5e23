//
//  IDFABaseViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFABaseViewController.h"

@implementation IDFABaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupBaseUI];
}

- (void)setupBaseUI {
    // 设置渐变背景
    self.gradientBackgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 设置滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view.mas_safeAreaLayoutGuide);
    }];
    
    // 内容视图
    self.contentView = [[UIView alloc] init];
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    [self setupIDFALogo];
}

- (void)setupIDFALogo {
    // 创建3D立体IDFA标题
    self.idfaLogoLabel = [[UILabel alloc] init];
    self.idfaLogoLabel.text = @"IDFA";
    self.idfaLogoLabel.font = [UIFont boldSystemFontOfSize:56];
    self.idfaLogoLabel.textAlignment = NSTextAlignmentCenter;

    // 设置渐变文字颜色
    self.idfaLogoLabel.textColor = [UIColor whiteColor];

    // 添加更强的阴影效果模拟3D立体感
    self.idfaLogoLabel.layer.shadowColor = [UIColor blackColor].CGColor;
    self.idfaLogoLabel.layer.shadowOffset = CGSizeMake(3, 3);
    self.idfaLogoLabel.layer.shadowRadius = 6;
    self.idfaLogoLabel.layer.shadowOpacity = 0.7;

    [self.contentView addSubview:self.idfaLogoLabel];
    [self.idfaLogoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(40);
        make.centerX.equalTo(self.contentView);
    }];
}

- (UIButton *)createGradientButtonWithTitle:(NSString *)title {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.titleLabel.font = [UIFont boldSystemFontOfSize:16];

    // 设置圆角
    button.layer.cornerRadius = 25;

    // 创建渐变背景
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.3 green:0.5 blue:1.0 alpha:0.8].CGColor,
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:0.9 alpha:0.8].CGColor
    ];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 1);
    gradientLayer.cornerRadius = 25;
    gradientLayer.frame = CGRectMake(0, 0, 200, 50); // 设置默认frame，会在layoutSubviews中更新

    [button.layer insertSublayer:gradientLayer atIndex:0];

    // 添加边框效果
    button.layer.borderWidth = 1;
    button.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;

    // 添加阴影
    button.layer.shadowColor = [UIColor blackColor].CGColor;
    button.layer.shadowOffset = CGSizeMake(0, 2);
    button.layer.shadowRadius = 4;
    button.layer.shadowOpacity = 0.3;
    button.layer.masksToBounds = NO;

    return button;
}

- (UILabel *)createInfoLabelWithText:(NSString *)text fontSize:(CGFloat)fontSize {
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont systemFontOfSize:fontSize];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 添加文字阴影
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(1, 1);
    label.layer.shadowRadius = 2;
    label.layer.shadowOpacity = 0.5;

    return label;
}

- (UILabel *)createTitleLabelWithText:(NSString *)text {
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont boldSystemFontOfSize:28];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 更强的阴影效果
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(2, 2);
    label.layer.shadowRadius = 4;
    label.layer.shadowOpacity = 0.7;

    return label;
}

- (UILabel *)createDescriptionLabelWithText:(NSString *)text {
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont systemFontOfSize:18];
    label.textColor = [UIColor colorWithWhite:0.95 alpha:1.0];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 设置行间距
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 6;
    paragraphStyle.alignment = NSTextAlignmentCenter;

    NSAttributedString *attributedString = [[NSAttributedString alloc]
        initWithString:text
        attributes:@{NSParagraphStyleAttributeName: paragraphStyle}];
    label.attributedText = attributedString;

    // 文字阴影
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(1, 1);
    label.layer.shadowRadius = 2;
    label.layer.shadowOpacity = 0.5;

    return label;
}

- (UIImageView *)createTappableImageViewWithImageName:(NSString *)imageName {
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.image = [UIImage imageNamed:imageName];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    imageView.userInteractionEnabled = YES;

    // 设置样式
    imageView.layer.cornerRadius = 12;
    imageView.layer.masksToBounds = YES;
    imageView.layer.borderWidth = 2;
    imageView.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;

    // 添加阴影
    imageView.layer.shadowColor = [UIColor blackColor].CGColor;
    imageView.layer.shadowOffset = CGSizeMake(0, 4);
    imageView.layer.shadowRadius = 8;
    imageView.layer.shadowOpacity = 0.3;
    imageView.layer.masksToBounds = NO;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self action:@selector(imageViewTapped:)];
    [imageView addGestureRecognizer:tapGesture];

    // 添加放大提示图标
    UIImageView *zoomIcon = [[UIImageView alloc] init];
    zoomIcon.image = [UIImage systemImageNamed:@"magnifyingglass"];
    zoomIcon.tintColor = [UIColor colorWithWhite:1.0 alpha:0.8];
    zoomIcon.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
    zoomIcon.layer.cornerRadius = 12;
    zoomIcon.layer.masksToBounds = YES;
    [imageView addSubview:zoomIcon];
    [zoomIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.equalTo(imageView).offset(-8);
        make.width.height.equalTo(@24);
    }];

    return imageView;
}

- (void)imageViewTapped:(UITapGestureRecognizer *)gesture {
    UIImageView *imageView = (UIImageView *)gesture.view;
    [self showImageZoomView:imageView.image];
}

- (void)showImageZoomView:(UIImage *)image {
    // 创建全屏背景
    UIView *backgroundView = [[UIView alloc] init];
    backgroundView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.9];
    backgroundView.alpha = 0;
    [self.view addSubview:backgroundView];
    [backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];

    // 创建放大的图片视图
    UIImageView *zoomedImageView = [[UIImageView alloc] init];
    zoomedImageView.image = image;
    zoomedImageView.contentMode = UIViewContentModeScaleAspectFit;
    zoomedImageView.layer.cornerRadius = 16;
    zoomedImageView.layer.masksToBounds = YES;
    zoomedImageView.transform = CGAffineTransformMakeScale(0.1, 0.1);
    [backgroundView addSubview:zoomedImageView];
    [zoomedImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(backgroundView);
        make.left.equalTo(backgroundView).offset(40);
        make.right.equalTo(backgroundView).offset(-40);
        make.height.lessThanOrEqualTo(backgroundView).multipliedBy(0.7);
    }];

    // 添加关闭按钮
    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeButton setImage:[UIImage systemImageNamed:@"xmark.circle.fill"] forState:UIControlStateNormal];
    closeButton.tintColor = [UIColor whiteColor];
    closeButton.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
    closeButton.layer.cornerRadius = 20;
    [closeButton addTarget:self action:@selector(closeImageZoomView:) forControlEvents:UIControlEventTouchUpInside];
    [backgroundView addSubview:closeButton];
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backgroundView.mas_safeAreaLayoutGuideTop).offset(20);
        make.right.equalTo(backgroundView).offset(-20);
        make.width.height.equalTo(@40);
    }];

    // 添加点击背景关闭手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self action:@selector(closeImageZoomView:)];
    [backgroundView addGestureRecognizer:tapGesture];

    // 动画显示
    [UIView animateWithDuration:0.3 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0 options:0 animations:^{
        backgroundView.alpha = 1;
        zoomedImageView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)closeImageZoomView:(id)sender {
    UIView *backgroundView = nil;
    if ([sender isKindOfClass:[UIButton class]]) {
        backgroundView = ((UIButton *)sender).superview;
    } else if ([sender isKindOfClass:[UITapGestureRecognizer class]]) {
        backgroundView = ((UITapGestureRecognizer *)sender).view;
    }

    if (backgroundView) {
        [UIView animateWithDuration:0.2 animations:^{
            backgroundView.alpha = 0;
            for (UIView *subview in backgroundView.subviews) {
                if ([subview isKindOfClass:[UIImageView class]]) {
                    subview.transform = CGAffineTransformMakeScale(0.1, 0.1);
                }
            }
        } completion:^(BOOL finished) {
            [backgroundView removeFromSuperview];
        }];
    }
}

- (void)showCopySuccessMessage {
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = @"已复制到剪贴板";
    messageLabel.font = [UIFont boldSystemFontOfSize:16];
    messageLabel.textColor = [UIColor whiteColor];
    messageLabel.textAlignment = NSTextAlignmentCenter;
    messageLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.8];
    messageLabel.layer.cornerRadius = 20;
    messageLabel.layer.masksToBounds = YES;
    messageLabel.alpha = 0;

    [self.view addSubview:messageLabel];
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.equalTo(@160);
        make.height.equalTo(@40);
    }];

    [UIView animateWithDuration:0.3 animations:^{
        messageLabel.alpha = 1;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 delay:1.5 options:0 animations:^{
            messageLabel.alpha = 0;
        } completion:^(BOOL finished) {
            [messageLabel removeFromSuperview];
        }];
    }];
}

@end

//
//  IDFABaseViewController.h
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import <UIKit/UIKit.h>
#import "IDFAGradientView.h"
#import "Masonry.h"

NS_ASSUME_NONNULL_BEGIN

@interface IDFABaseViewController : UIViewController

@property (nonatomic, strong) IDFAGradientView *gradientBackgroundView;
@property (nonatomic, strong) UILabel *idfaLogoLabel;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;

// 设置基础UI
- (void)setupBaseUI;

// 创建3D IDFA标题
- (void)setupIDFALogo;

// 创建渐变按钮
- (UIButton *)createGradientButtonWithTitle:(NSString *)title;

// 创建信息标签
- (UILabel *)createInfoLabelWithText:(NSString *)text fontSize:(CGFloat)fontSize;

// 显示复制成功提示
- (void)showCopySuccessMessage;

@end

NS_ASSUME_NONNULL_END

//
//  IDFAAuthorizedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAAuthorizedViewController.h"
#import "IDFADescriptionView.h"

@interface IDFAAuthorizedViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIView *idfaContainerView;
@property (nonatomic, strong) UILabel *idfaValueLabel;
@property (nonatomic, strong) UIButton *idfaCopyButton;
@property (nonatomic, strong) UIButton *shareButton;
@property (nonatomic, strong) IDFADescriptionView *descriptionView;
@end

@implementation IDFAAuthorizedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self getIDFAValue];
    [self setupUI];
}

- (void)getIDFAValue {
    NSUUID *uuid = [ASIdentifierManager sharedManager].advertisingIdentifier;
    self.idfaString = [uuid UUIDString];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"✅ 已获取IDFA权限"];
    self.statusLabel.textColor = theme.successTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.largeSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // IDFA容器视图
    self.idfaContainerView = [[UIView alloc] init];
    self.idfaContainerView.backgroundColor = theme.containerBackgroundColor;
    self.idfaContainerView.layer.cornerRadius = theme.containerCornerRadius;
    self.idfaContainerView.layer.borderWidth = theme.borderWidth;
    self.idfaContainerView.layer.borderColor = theme.borderColor.CGColor;

    // 添加阴影
    self.idfaContainerView.layer.shadowColor = [UIColor blackColor].CGColor;
    self.idfaContainerView.layer.shadowOffset = theme.shadowOffset;
    self.idfaContainerView.layer.shadowRadius = theme.shadowRadius;
    self.idfaContainerView.layer.shadowOpacity = theme.shadowOpacity;
    self.idfaContainerView.layer.masksToBounds = NO;

    [self.contentView addSubview:self.idfaContainerView];
    [self.idfaContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.height.equalTo(@120);
    }];
    
    // IDFA标签
    UILabel *idfaLabel = [self createInfoLabelWithText:@"您的IDFA:" fontSize:theme.titleFontSize - 2];
    idfaLabel.font = [UIFont boldSystemFontOfSize:theme.titleFontSize - 2];
    [self.idfaContainerView addSubview:idfaLabel];
    [idfaLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaContainerView).offset(theme.smallSpacing);
        make.centerX.equalTo(self.idfaContainerView);
    }];

    // IDFA值
    self.idfaValueLabel = [self createInfoLabelWithText:self.idfaString fontSize:theme.idfaValueFontSize];
    self.idfaValueLabel.font = [UIFont fontWithName:@"Courier" size:theme.idfaValueFontSize]; // 使用等宽字体
    self.idfaValueLabel.numberOfLines = 0;
    self.idfaValueLabel.textColor = theme.idfaTextColor;

    // 设置选择功能
    self.idfaValueLabel.userInteractionEnabled = YES;
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc]
        initWithTarget:self action:@selector(idfaLabelLongPressed:)];
    [self.idfaValueLabel addGestureRecognizer:longPress];

    [self.idfaContainerView addSubview:self.idfaValueLabel];
    [self.idfaValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(idfaLabel.mas_bottom).offset(theme.smallSpacing);
        make.left.equalTo(self.idfaContainerView).offset(theme.smallSpacing);
        make.right.equalTo(self.idfaContainerView).offset(-theme.smallSpacing);
        make.bottom.lessThanOrEqualTo(self.idfaContainerView).offset(-theme.smallSpacing);
    }];
    
    // 按钮容器
    UIView *buttonContainer = [[UIView alloc] init];
    [self.idfaContainerView addSubview:buttonContainer];
    [buttonContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.idfaContainerView).offset(-8);
        make.left.equalTo(self.idfaContainerView).offset(theme.horizontalMargin);
        make.right.equalTo(self.idfaContainerView).offset(-theme.horizontalMargin);
        make.height.equalTo(@44);
    }];

    // 复制按钮
    self.idfaCopyButton = [self createGradientButtonWithTitle:@"📋 复制IDFA"];
    self.idfaCopyButton.titleLabel.font = [UIFont boldSystemFontOfSize:theme.smallButtonFontSize];
    [self.idfaCopyButton addTarget:self action:@selector(copyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [buttonContainer addSubview:self.idfaCopyButton];
    [self.idfaCopyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(buttonContainer);
        make.top.bottom.equalTo(buttonContainer);
        make.width.equalTo(@135);
    }];

    // 分享按钮
    self.shareButton = [self createGradientButtonWithTitle:@"📤 分享"];
    self.shareButton.titleLabel.font = [UIFont boldSystemFontOfSize:theme.smallButtonFontSize];
    [self.shareButton addTarget:self action:@selector(shareButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [buttonContainer addSubview:self.shareButton];
    [self.shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(buttonContainer);
        make.top.bottom.equalTo(buttonContainer);
        make.width.equalTo(@135);
    }];
    
    // IDFA说明
    // IDFA是IOS设备的广告标识符，可以简单理解为IOS设备的临时身份证。仅能用于跟踪广告效果，打通不同App之间的广告。
    self.descriptionView = [[IDFADescriptionView alloc] init];
    NSArray *descriptions = @[
        @"什么是IDFA？",
        @"IDFA是iOS设备的广告标识符",
        @"可以简单理解为iOS设备的临时身份证",
        @"仅能用于跟踪广告效果",
        @"打通不同App之间的广告"
    ];
    [self.descriptionView setupWithDescriptions:descriptions];
    [self.contentView addSubview:self.descriptionView];
    [self.descriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(buttonContainer.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
    
    // 添加按钮动画
    [self addButtonAnimation:self.idfaCopyButton];
    [self addButtonAnimation:self.shareButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)idfaLabelLongPressed:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        [self copyButtonTapped];
    }
}

- (void)copyButtonTapped {
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = self.idfaString;
    [self showCopySuccessMessage];
}

- (void)shareButtonTapped {
    NSString *shareText = [NSString stringWithFormat:@"我的IDFA: %@", self.idfaString];
    UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:@[shareText] applicationActivities:nil];
    
    // iPad适配
    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        activityVC.popoverPresentationController.sourceView = self.shareButton;
        activityVC.popoverPresentationController.sourceRect = self.shareButton.bounds;
    }
    
    [self presentViewController:activityVC animated:YES completion:nil];
}

@end

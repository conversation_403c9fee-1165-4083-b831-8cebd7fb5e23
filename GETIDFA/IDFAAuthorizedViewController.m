//
//  IDFAAuthorizedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAAuthorizedViewController.h"

@interface IDFAAuthorizedViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIView *idfaContainerView;
@property (nonatomic, strong) UILabel *idfaValueLabel;
@property (nonatomic, strong) UIButton *idfaCopyButton;
@property (nonatomic, strong) UIButton *shareButton;
@property (nonatomic, strong) UILabel *explanationLabel;
@end

@implementation IDFAAuthorizedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self getIDFAValue];
    [self setupUI];
}

- (void)getIDFAValue {
    NSUUID *uuid = [ASIdentifierManager sharedManager].advertisingIdentifier;
    self.idfaString = [uuid UUIDString];
}

- (void)setupUI {
    // 状态标题
    self.statusLabel = [self createInfoLabelWithText:@"✅ 已获取IDFA权限" fontSize:24];
    self.statusLabel.textColor = [UIColor colorWithRed:0.3 green:0.9 blue:0.3 alpha:1.0];
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(40);
        make.centerX.equalTo(self.contentView);
    }];
    
    // IDFA容器视图
    self.idfaContainerView = [[UIView alloc] init];
    self.idfaContainerView.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1];
    self.idfaContainerView.layer.cornerRadius = 15;
    self.idfaContainerView.layer.borderWidth = 1;
    self.idfaContainerView.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    [self.contentView addSubview:self.idfaContainerView];
    [self.idfaContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
        make.height.equalTo(@120);
    }];
    
    // IDFA标签
    UILabel *idfaLabel = [self createInfoLabelWithText:@"您的IDFA:" fontSize:16];
    [self.idfaContainerView addSubview:idfaLabel];
    [idfaLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaContainerView).offset(15);
        make.centerX.equalTo(self.idfaContainerView);
    }];
    
    // IDFA值
    self.idfaValueLabel = [self createInfoLabelWithText:self.idfaString fontSize:14];
    self.idfaValueLabel.font = [UIFont fontWithName:@"Courier" size:14]; // 使用等宽字体
    self.idfaValueLabel.numberOfLines = 2;
    [self.idfaContainerView addSubview:self.idfaValueLabel];
    [self.idfaValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(idfaLabel.mas_bottom).offset(10);
        make.left.equalTo(self.idfaContainerView).offset(15);
        make.right.equalTo(self.idfaContainerView).offset(-15);
    }];
    
    // 按钮容器
    UIView *buttonContainer = [[UIView alloc] init];
    [self.contentView addSubview:buttonContainer];
    [buttonContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaContainerView.mas_bottom).offset(30);
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(@280);
        make.height.equalTo(@50);
    }];
    
    // 复制按钮
    self.idfaCopyButton = [self createGradientButtonWithTitle:@"复制"];
    [self.idfaCopyButton addTarget:self action:@selector(copyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [buttonContainer addSubview:self.idfaCopyButton];
    [self.idfaCopyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(buttonContainer);
        make.top.bottom.equalTo(buttonContainer);
        make.width.equalTo(@130);
    }];
    
    // 分享按钮
    self.shareButton = [self createGradientButtonWithTitle:@"分享"];
    [self.shareButton addTarget:self action:@selector(shareButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [buttonContainer addSubview:self.shareButton];
    [self.shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(buttonContainer);
        make.top.bottom.equalTo(buttonContainer);
        make.width.equalTo(@130);
    }];
    
    // IDFA说明
    NSString *explanationText = @"IDFA已成功获取！\n\n此标识符可用于：\n• 个性化广告投放\n• 应用数据分析\n• 跨应用行为统计\n\n您可以随时在系统设置中关闭此权限。";
    self.explanationLabel = [self createInfoLabelWithText:explanationText fontSize:14];
    [self.contentView addSubview:self.explanationLabel];
    [self.explanationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(buttonContainer.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(30);
        make.right.equalTo(self.contentView).offset(-30);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
    
    // 添加按钮动画
    [self addButtonAnimation:self.idfaCopyButton];
    [self addButtonAnimation:self.shareButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)copyButtonTapped {
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = self.idfaString;
    [self showCopySuccessMessage];
}

- (void)shareButtonTapped {
    NSString *shareText = [NSString stringWithFormat:@"我的IDFA: %@", self.idfaString];
    UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:@[shareText] applicationActivities:nil];
    
    // iPad适配
    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        activityVC.popoverPresentationController.sourceView = self.shareButton;
        activityVC.popoverPresentationController.sourceRect = self.shareButton.bounds;
    }
    
    [self presentViewController:activityVC animated:YES completion:nil];
}

@end

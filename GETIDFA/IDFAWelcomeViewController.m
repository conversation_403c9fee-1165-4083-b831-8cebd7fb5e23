//
//  IDFAWelcomeViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAWelcomeViewController.h"
#import "IDFAThemeManager.h"
#import "IDFAGradientView.h"
#import "IDFAGradientButton.h"
#import "Masonry.h"

@interface IDFAWelcomeViewController ()
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) IDFAGradientButton *agreeButton;
@property (nonatomic, strong) UIButton *disagreeButton;
@end

@implementation IDFAWelcomeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 背景遮罩
    self.view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.8];
    
    // 容器视图
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = theme.containerBackgroundColor;
    self.containerView.layer.cornerRadius = theme.containerCornerRadius;
    self.containerView.layer.borderWidth = theme.borderWidth;
    self.containerView.layer.borderColor = theme.borderColor.CGColor;
    
    // 添加阴影
    self.containerView.layer.shadowColor = [UIColor blackColor].CGColor;
    self.containerView.layer.shadowOffset = theme.shadowOffset;
    self.containerView.layer.shadowRadius = theme.shadowRadius;
    self.containerView.layer.shadowOpacity = theme.shadowOpacity;
    self.containerView.layer.masksToBounds = NO;
    
    [self.view addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
    }];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"欢迎使用【GETIDFA】";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:theme.titleFontSize];
    self.titleLabel.textColor = theme.primaryTextColor;
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.containerView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.containerView).offset(25);
        make.left.equalTo(self.containerView).offset(20);
        make.right.equalTo(self.containerView).offset(-20);
    }];
    
    // 内容文字
    [self setupContentLabel];
    
    // 同意按钮
    self.agreeButton = [[IDFAGradientButton alloc] initWithTitle:@"同意并继续"];
    [self.agreeButton addTarget:self action:@selector(agreeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.agreeButton];
    [self.agreeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentLabel.mas_bottom).offset(25);
        make.left.equalTo(self.containerView).offset(20);
        make.right.equalTo(self.containerView).offset(-20);
        make.height.equalTo(@44);
    }];
    
    // 不同意按钮
    self.disagreeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.disagreeButton setTitle:@"不同意" forState:UIControlStateNormal];
    [self.disagreeButton setTitleColor:theme.hintTextColor forState:UIControlStateNormal];
    self.disagreeButton.titleLabel.font = [UIFont systemFontOfSize:theme.hintFontSize];
    [self.disagreeButton addTarget:self action:@selector(disagreeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.disagreeButton];
    [self.disagreeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.agreeButton.mas_bottom).offset(15);
        make.centerX.equalTo(self.containerView);
        make.bottom.equalTo(self.containerView).offset(-20);
        make.height.equalTo(@30);
    }];
}

- (void)setupContentLabel {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    NSString *fullText = @"请充分阅读并理解用户协议和隐私政策。请在同意并接受全部条款后再开始使用我们的服务，感谢你的支持";
    
    // 创建富文本
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:fullText];
    
    // 设置默认样式
    [attributedString addAttribute:NSFontAttributeName 
                             value:[UIFont systemFontOfSize:theme.descriptionFontSize] 
                             range:NSMakeRange(0, fullText.length)];
    [attributedString addAttribute:NSForegroundColorAttributeName 
                             value:theme.secondaryTextColor 
                             range:NSMakeRange(0, fullText.length)];
    
    // 设置段落样式
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 4;
    paragraphStyle.alignment = NSTextAlignmentCenter;
    [attributedString addAttribute:NSParagraphStyleAttributeName 
                             value:paragraphStyle 
                             range:NSMakeRange(0, fullText.length)];
    
    // 设置"用户协议"为蓝色可点击
    NSRange userAgreementRange = [fullText rangeOfString:@"用户协议"];
    if (userAgreementRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0]
                                 range:userAgreementRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:userAgreementRange];
        // 添加字体加粗
        [attributedString addAttribute:NSFontAttributeName
                                 value:[UIFont boldSystemFontOfSize:theme.descriptionFontSize]
                                 range:userAgreementRange];
    }

    // 设置"隐私政策"为蓝色可点击
    NSRange privacyPolicyRange = [fullText rangeOfString:@"隐私政策"];
    if (privacyPolicyRange.location != NSNotFound) {
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:[UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0]
                                 range:privacyPolicyRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName
                                 value:@(NSUnderlineStyleSingle)
                                 range:privacyPolicyRange];
        // 添加字体加粗
        [attributedString addAttribute:NSFontAttributeName
                                 value:[UIFont boldSystemFontOfSize:theme.descriptionFontSize]
                                 range:privacyPolicyRange];
    }
    
    self.contentLabel = [[UILabel alloc] init];
    self.contentLabel.attributedText = attributedString;
    self.contentLabel.numberOfLines = 0;
    self.contentLabel.userInteractionEnabled = YES;
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] 
                                         initWithTarget:self 
                                                 action:@selector(contentLabelTapped:)];
    [self.contentLabel addGestureRecognizer:tapGesture];
    
    [self.containerView addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(20);
        make.left.equalTo(self.containerView).offset(20);
        make.right.equalTo(self.containerView).offset(-20);
    }];
}



- (void)contentLabelTapped:(UITapGestureRecognizer *)gesture {
    CGPoint location = [gesture locationInView:self.contentLabel];

    // 获取文本布局管理器
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:self.contentLabel.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:self.contentLabel.bounds.size];

    [textStorage addLayoutManager:layoutManager];
    [layoutManager addTextContainer:textContainer];

    textContainer.lineFragmentPadding = 0;
    textContainer.maximumNumberOfLines = self.contentLabel.numberOfLines;
    textContainer.lineBreakMode = self.contentLabel.lineBreakMode;

    // 获取点击位置对应的字符索引
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:location
                                                       inTextContainer:textContainer
                              fractionOfDistanceBetweenInsertionPoints:NULL];

    // 检测点击的是哪个链接
    NSString *fullText = self.contentLabel.attributedText.string;
    NSRange userAgreementRange = [fullText rangeOfString:@"用户协议"];
    NSRange privacyPolicyRange = [fullText rangeOfString:@"隐私政策"];

    if (userAgreementRange.location != NSNotFound &&
        characterIndex >= userAgreementRange.location &&
        characterIndex < userAgreementRange.location + userAgreementRange.length) {
        // 点击了用户协议
        if ([self.delegate respondsToSelector:@selector(welcomeViewControllerDidTapUserAgreement)]) {
            [self.delegate welcomeViewControllerDidTapUserAgreement];
        }
    } else if (privacyPolicyRange.location != NSNotFound &&
               characterIndex >= privacyPolicyRange.location &&
               characterIndex < privacyPolicyRange.location + privacyPolicyRange.length) {
        // 点击了隐私政策
        if ([self.delegate respondsToSelector:@selector(welcomeViewControllerDidTapPrivacyPolicy)]) {
            [self.delegate welcomeViewControllerDidTapPrivacyPolicy];
        }
    }
}

#pragma mark - Actions

- (void)agreeButtonTapped {
    if ([self.delegate respondsToSelector:@selector(welcomeViewControllerDidAgree)]) {
        [self.delegate welcomeViewControllerDidAgree];
    }
}

- (void)disagreeButtonTapped {
    if ([self.delegate respondsToSelector:@selector(welcomeViewControllerDidDisagree)]) {
        [self.delegate welcomeViewControllerDidDisagree];
    }
}

@end

//
//  IDFADocumentViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADocumentViewController.h"
#import "IDFAThemeManager.h"
#import "IDFAGradientView.h"
#import "IDFAGradientButton.h"
#import "Masonry.h"

@interface IDFADocumentViewController ()
@property (nonatomic, assign) IDFADocumentType documentType;
@property (nonatomic, strong) IDFAGradientView *gradientBackgroundView;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) IDFAGradientButton *closeButton;
@end

@implementation IDFADocumentViewController

- (instancetype)initWithDocumentType:(IDFADocumentType)type {
    self = [super init];
    if (self) {
        _documentType = type;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self loadContent];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    // 确保动画在视图完全显示后启动
    NSLog(@"IDFADocumentViewController viewDidAppear - 渐变视图frame: %@", NSStringFromCGRect(self.gradientBackgroundView.frame));

    // 强制重新启动动画
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.gradientBackgroundView restartAnimations];
    });
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 渐变背景
    self.gradientBackgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-80);
    }];
    
    // 内容视图
    self.contentView = [[UIView alloc] init];
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:theme.titleFontSize + 4];
    self.titleLabel.textColor = theme.primaryTextColor;
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.numberOfLines = 0;
    
    // 添加标题阴影
    self.titleLabel.layer.shadowColor = [UIColor blackColor].CGColor;
    self.titleLabel.layer.shadowOffset = theme.shadowOffset;
    self.titleLabel.layer.shadowRadius = theme.shadowRadius;
    self.titleLabel.layer.shadowOpacity = theme.shadowOpacity;
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(theme.smallSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];
    
    // 内容
    self.contentLabel = [[UILabel alloc] init];
    self.contentLabel.font = [UIFont systemFontOfSize:theme.descriptionFontSize];
    self.contentLabel.textColor = theme.secondaryTextColor;
    self.contentLabel.numberOfLines = 0;
    
    // 设置行间距
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 6;
    paragraphStyle.alignment = NSTextAlignmentLeft;
    
    [self.contentView addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(theme.largeSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.bottom.equalTo(self.contentView).offset(-theme.largeSpacing);
    }];
    
    // 关闭按钮 - 使用小尺寸
    self.closeButton = [[IDFAGradientButton alloc] initWithTitle:@"关闭" size:IDFAButtonSizeSmall];
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-theme.horizontalMargin);
        make.centerX.equalTo(self.view);
        make.width.equalTo(@160);
        make.height.equalTo(@([self.closeButton recommendedHeight]));
    }];
}

- (void)loadContent {
    NSString *fileName = @"";
    NSString *title = @"";
    
    switch (self.documentType) {
        case IDFADocumentTypeAbout:
            fileName = @"关于";
            title = @"关于";
            break;
        case IDFADocumentTypeUserAgreement:
            fileName = @"用户协议";
            title = @"用户协议";
            break;
        case IDFADocumentTypePrivacyPolicy:
            fileName = @"隐私政策";
            title = @"隐私政策";
            break;
    }
    
    self.titleLabel.text = title;
    
    // 读取文件内容
    NSString *filePath = [[NSBundle mainBundle] pathForResource:fileName ofType:@"txt"];
    NSString *content = @"";
    
    if (filePath) {
        NSError *error;
        content = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:&error];
        if (error) {
            content = @"文件读取失败，请稍后重试。";
        }
    } else {
        content = @"文件不存在，请联系开发者。";
    }
    
    // 设置内容
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 6;
    paragraphStyle.alignment = NSTextAlignmentLeft;
    
    NSAttributedString *attributedContent = [[NSAttributedString alloc] 
        initWithString:content 
        attributes:@{
            NSParagraphStyleAttributeName: paragraphStyle,
            NSFontAttributeName: [UIFont systemFontOfSize:theme.descriptionFontSize],
            NSForegroundColorAttributeName: theme.secondaryTextColor
        }];
    
    self.contentLabel.attributedText = attributedContent;
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

@end

//
//  IDFARestrictedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFARestrictedViewController.h"

@interface IDFARestrictedViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *explanationLabel;
@property (nonatomic, strong) UIImageView *iconImageView;
@end

@implementation IDFARestrictedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // 图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.image = [UIImage systemImageNamed:@"lock.shield"];
    self.iconImageView.tintColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(50);
        make.centerX.equalTo(self.contentView);
        make.width.height.equalTo(@100);
    }];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"🔒 跟踪功能受限"];
    self.statusLabel.textColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView.mas_bottom).offset(40);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
    }];

    // 说明文字
    NSString *explanationText = @"跟踪功能受限，无法使用此功能。\n\n这通常是由于：\n\n• 设备管理策略限制\n• 家长控制设置\n• 企业设备管理限制\n• 系统安全策略\n\n请联系设备管理员或检查相关设置。";
    self.explanationLabel = [self createDescriptionLabelWithText:explanationText];
    [self.contentView addSubview:self.explanationLabel];
    [self.explanationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(40);
        make.left.equalTo(self.contentView).offset(25);
        make.right.equalTo(self.contentView).offset(-25);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
}

@end

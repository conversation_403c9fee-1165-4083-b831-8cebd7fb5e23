//
//  IDFARestrictedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFARestrictedViewController.h"
#import "IDFADescriptionView.h"

@interface IDFARestrictedViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) IDFADescriptionView *descriptionView;
@property (nonatomic, strong) UIImageView *iconImageView;
@end

@implementation IDFARestrictedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.image = [UIImage systemImageNamed:@"lock.shield"];
    self.iconImageView.tintColor = theme.warningTextColor;
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.largeSpacing);
        make.centerX.equalTo(self.contentView);
        make.width.height.equalTo(@80);
    }];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"🔒 跟踪功能受限"];
    self.statusLabel.textColor = theme.warningTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    self.descriptionView = [[IDFADescriptionView alloc] init];
    NSArray *descriptions = @[
        @"跟踪功能受限，无法使用此功能。",
        @"设备管理策略限制",
        @"家长控制设置",
        @"企业设备管理限制",
        @"系统安全策略",
        @"请联系设备管理员或检查相关设置。"
    ];
    [self.descriptionView setupWithDescriptions:descriptions];
    [self.contentView addSubview:self.descriptionView];
    [self.descriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
}

@end

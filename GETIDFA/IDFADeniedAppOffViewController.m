//
//  IDFADeniedAppOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedAppOffViewController.h"

@interface IDFADeniedAppOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@property (nonatomic, strong) UIButton *openSettingsButton;
@property (nonatomic, strong) UILabel *manualStepsLabel;
@end

@implementation IDFADeniedAppOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // 状态标题
    self.statusLabel = [self createInfoLabelWithText:@"❌ 应用跟踪权限已拒绝" fontSize:24];
    self.statusLabel.textColor = [UIColor colorWithRed:1.0 green:0.3 blue:0.3 alpha:1.0];
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(40);
        make.centerX.equalTo(self.contentView);
    }];
    
    // 说明文字
    NSString *instructionText = @"您已拒绝跟踪权限。如需启用，请前往本应用设置页打开跟踪权限。";
    self.instructionLabel = [self createInfoLabelWithText:instructionText fontSize:16];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(30);
        make.right.equalTo(self.contentView).offset(-30);
    }];
    
    // 前往设置按钮
    self.openSettingsButton = [self createGradientButtonWithTitle:@"前往开启"];
    [self.openSettingsButton addTarget:self action:@selector(openSettingsButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.openSettingsButton];
    [self.openSettingsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(30);
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(@200);
        make.height.equalTo(@50);
    }];
    
    // 手动操作步骤标题
    UILabel *manualTitleLabel = [self createInfoLabelWithText:@"或者您也可以手动前往：" fontSize:16];
    [self.contentView addSubview:manualTitleLabel];
    [manualTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.openSettingsButton.mas_bottom).offset(40);
        make.left.equalTo(self.contentView).offset(30);
        make.right.equalTo(self.contentView).offset(-30);
    }];

    // 步骤引导容器
    UIStackView *stepsStackView = [[UIStackView alloc] init];
    stepsStackView.axis = UILayoutConstraintAxisVertical;
    stepsStackView.spacing = 15;
    stepsStackView.alignment = UIStackViewAlignmentFill;
    [self.contentView addSubview:stepsStackView];
    [stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(manualTitleLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
    }];

    // 添加步骤
    [self addStepToStackView:stepsStackView withNumber:1 title:@"打开系统设置" imageName:@"guide_AppOff_01"];
    [self addStepToStackView:stepsStackView withNumber:2 title:@"找到并进入IDFA应用" imageName:@"guide_AppOff_02"];
    [self addStepToStackView:stepsStackView withNumber:3 title:@"开启\"允许跟踪\"" imageName:@"guide_AppOff_03"];

    // 底部提示
    UILabel *bottomTipLabel = [self createInfoLabelWithText:@"开启后请返回应用重新获取IDFA" fontSize:14];
    bottomTipLabel.textColor = [UIColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0];
    [self.contentView addSubview:bottomTipLabel];
    [bottomTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(stepsStackView.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(30);
        make.right.equalTo(self.contentView).offset(-30);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
    
    // 添加按钮动画
    [self addButtonAnimation:self.openSettingsButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)openSettingsButtonTapped {
    NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if ([[UIApplication sharedApplication] canOpenURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    }
}

- (void)addStepToStackView:(UIStackView *)stackView withNumber:(NSInteger)stepNumber title:(NSString *)title imageName:(NSString *)imageName {
    // 步骤容器
    UIView *stepContainer = [[UIView alloc] init];
    stepContainer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1];
    stepContainer.layer.cornerRadius = 12;
    stepContainer.layer.borderWidth = 1;
    stepContainer.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    [stackView addArrangedSubview:stepContainer];
    [stepContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@80);
    }];

    // 步骤编号
    UILabel *stepNumberLabel = [[UILabel alloc] init];
    stepNumberLabel.text = [NSString stringWithFormat:@"%ld", (long)stepNumber];
    stepNumberLabel.font = [UIFont boldSystemFontOfSize:16];
    stepNumberLabel.textColor = [UIColor whiteColor];
    stepNumberLabel.textAlignment = NSTextAlignmentCenter;
    stepNumberLabel.backgroundColor = [UIColor colorWithRed:0.3 green:0.5 blue:1.0 alpha:0.8];
    stepNumberLabel.layer.cornerRadius = 12;
    stepNumberLabel.layer.masksToBounds = YES;
    [stepContainer addSubview:stepNumberLabel];
    [stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepContainer).offset(12);
        make.centerY.equalTo(stepContainer);
        make.width.height.equalTo(@24);
    }];

    // 步骤标题
    UILabel *stepTitleLabel = [self createInfoLabelWithText:title fontSize:14];
    stepTitleLabel.textAlignment = NSTextAlignmentLeft;
    [stepContainer addSubview:stepTitleLabel];
    [stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepNumberLabel.mas_right).offset(12);
        make.centerY.equalTo(stepContainer);
        make.right.equalTo(stepContainer).offset(-80);
    }];

    // 引导图片
    UIImageView *guideImageView = [[UIImageView alloc] init];
    guideImageView.image = [UIImage imageNamed:imageName];
    guideImageView.contentMode = UIViewContentModeScaleAspectFit;
    guideImageView.layer.cornerRadius = 6;
    guideImageView.layer.masksToBounds = YES;
    guideImageView.layer.borderWidth = 1;
    guideImageView.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    [stepContainer addSubview:guideImageView];
    [guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(stepContainer).offset(-12);
        make.centerY.equalTo(stepContainer);
        make.width.equalTo(@60);
        make.height.equalTo(@60);
    }];
}

@end

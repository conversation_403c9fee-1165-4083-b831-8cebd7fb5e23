//
//  IDFABottomToolbar.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFABottomToolbar.h"
#import "IDFAThemeManager.h"
#import "Masonry.h"

@interface IDFABottomToolbar ()
@property (nonatomic, strong) UIButton *aboutUsButton;
@property (nonatomic, strong) UIButton *userAgreementButton;
@property (nonatomic, strong) UIButton *privacyPolicyButton;
@property (nonatomic, strong) UIView *separatorView1;
@property (nonatomic, strong) UIView *separatorView2;
@end

@implementation IDFABottomToolbar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    NSLog(@"设置底部工具栏UI");

    // 背景已在MainViewController中设置
    
    // 关于我们按钮
    self.aboutUsButton = [self createToolbarButtonWithTitle:@"关于我们"];
    [self.aboutUsButton addTarget:self action:@selector(aboutUsButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.aboutUsButton];
    
    // 用户协议按钮
    self.userAgreementButton = [self createToolbarButtonWithTitle:@"用户协议"];
    [self.userAgreementButton addTarget:self action:@selector(userAgreementButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.userAgreementButton];
    
    // 隐私政策按钮
    self.privacyPolicyButton = [self createToolbarButtonWithTitle:@"隐私政策"];
    [self.privacyPolicyButton addTarget:self action:@selector(privacyPolicyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.privacyPolicyButton];
    
    // 分隔线1
    self.separatorView1 = [[UIView alloc] init];
    self.separatorView1.backgroundColor = theme.hintTextColor;
    [self addSubview:self.separatorView1];
    
    // 分隔线2
    self.separatorView2 = [[UIView alloc] init];
    self.separatorView2.backgroundColor = theme.hintTextColor;
    [self addSubview:self.separatorView2];
    
    [self setupConstraints];

    NSLog(@"底部工具栏UI设置完成，按钮数量：%lu", (unsigned long)self.subviews.count);
}

- (void)setupConstraints {
    // 使用等宽布局，让三个按钮平均分布
    [self.aboutUsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.centerY.equalTo(self);
        make.height.equalTo(@30);
        make.width.equalTo(self.userAgreementButton);
    }];

    [self.userAgreementButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.equalTo(@30);
        make.width.equalTo(self.privacyPolicyButton);
    }];

    [self.privacyPolicyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-15);
        make.centerY.equalTo(self);
        make.height.equalTo(@30);
        make.width.equalTo(self.aboutUsButton);
    }];

    // 分隔线1 - 在关于我们和用户协议之间
    [self.separatorView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.aboutUsButton.mas_right).offset(10);
        make.right.equalTo(self.userAgreementButton.mas_left).offset(-10);
        make.centerY.equalTo(self);
        make.width.equalTo(@1);
        make.height.equalTo(@20);
    }];

    // 分隔线2 - 在用户协议和隐私政策之间
    [self.separatorView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.userAgreementButton.mas_right).offset(10);
        make.right.equalTo(self.privacyPolicyButton.mas_left).offset(-10);
        make.centerY.equalTo(self);
        make.width.equalTo(@1);
        make.height.equalTo(@20);
    }];
}

- (UIButton *)createToolbarButtonWithTitle:(NSString *)title {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:[UIColor colorWithWhite:0.9 alpha:1.0] forState:UIControlStateNormal];
    [button setTitleColor:[UIColor colorWithRed:0.3 green:0.6 blue:1.0 alpha:1.0] forState:UIControlStateHighlighted];
    button.titleLabel.font = [UIFont systemFontOfSize:theme.descriptionFontSize - 2];

    // 添加按压动画
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];

    return button;
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    [self.aboutUsButton setTitleColor:[UIColor colorWithWhite:0.9 alpha:1.0] forState:UIControlStateNormal];
    [self.userAgreementButton setTitleColor:[UIColor colorWithWhite:0.9 alpha:1.0] forState:UIControlStateNormal];
    [self.privacyPolicyButton setTitleColor:[UIColor colorWithWhite:0.9 alpha:1.0] forState:UIControlStateNormal];

    self.separatorView1.backgroundColor = [UIColor colorWithWhite:0.7 alpha:1.0];
    self.separatorView2.backgroundColor = [UIColor colorWithWhite:0.7 alpha:1.0];
}

#pragma mark - Button Animation

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
        button.alpha = 0.7;
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
        button.alpha = 1.0;
    }];
}

#pragma mark - Actions

- (void)aboutUsButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapAboutUs)]) {
        [self.delegate bottomToolbarDidTapAboutUs];
    }
}

- (void)userAgreementButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapUserAgreement)]) {
        [self.delegate bottomToolbarDidTapUserAgreement];
    }
}

- (void)privacyPolicyButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapPrivacyPolicy)]) {
        [self.delegate bottomToolbarDidTapPrivacyPolicy];
    }
}

@end

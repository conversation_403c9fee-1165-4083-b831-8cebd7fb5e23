//
//  IDFABottomToolbar.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFABottomToolbar.h"
#import "IDFAThemeManager.h"
#import "Masonry.h"

@interface IDFABottomToolbar ()
@property (nonatomic, strong) UIButton *aboutUsButton;
@property (nonatomic, strong) UIButton *userAgreementButton;
@property (nonatomic, strong) UIButton *privacyPolicyButton;
@property (nonatomic, strong) UIView *separatorView1;
@property (nonatomic, strong) UIView *separatorView2;
@end

@implementation IDFABottomToolbar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 设置背景
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
    
    // 关于我们按钮
    self.aboutUsButton = [self createToolbarButtonWithTitle:@"关于我们"];
    [self.aboutUsButton addTarget:self action:@selector(aboutUsButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.aboutUsButton];
    
    // 用户协议按钮
    self.userAgreementButton = [self createToolbarButtonWithTitle:@"用户协议"];
    [self.userAgreementButton addTarget:self action:@selector(userAgreementButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.userAgreementButton];
    
    // 隐私政策按钮
    self.privacyPolicyButton = [self createToolbarButtonWithTitle:@"隐私政策"];
    [self.privacyPolicyButton addTarget:self action:@selector(privacyPolicyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.privacyPolicyButton];
    
    // 分隔线1
    self.separatorView1 = [[UIView alloc] init];
    self.separatorView1.backgroundColor = theme.hintTextColor;
    [self addSubview:self.separatorView1];
    
    // 分隔线2
    self.separatorView2 = [[UIView alloc] init];
    self.separatorView2.backgroundColor = theme.hintTextColor;
    [self addSubview:self.separatorView2];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    // 关于我们按钮
    [self.aboutUsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(20);
        make.centerY.equalTo(self);
        make.height.equalTo(@30);
    }];
    
    // 分隔线1
    [self.separatorView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.aboutUsButton.mas_right).offset(15);
        make.centerY.equalTo(self);
        make.width.equalTo(@1);
        make.height.equalTo(@16);
    }];
    
    // 用户协议按钮
    [self.userAgreementButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.separatorView1.mas_right).offset(15);
        make.centerY.equalTo(self);
        make.height.equalTo(@30);
    }];
    
    // 分隔线2
    [self.separatorView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.userAgreementButton.mas_right).offset(15);
        make.centerY.equalTo(self);
        make.width.equalTo(@1);
        make.height.equalTo(@16);
    }];
    
    // 隐私政策按钮
    [self.privacyPolicyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.separatorView2.mas_right).offset(15);
        make.centerY.equalTo(self);
        make.height.equalTo(@30);
        make.right.lessThanOrEqualTo(self).offset(-20);
    }];
}

- (UIButton *)createToolbarButtonWithTitle:(NSString *)title {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:theme.hintTextColor forState:UIControlStateNormal];
    [button setTitleColor:theme.secondaryTextColor forState:UIControlStateHighlighted];
    button.titleLabel.font = [UIFont systemFontOfSize:theme.hintFontSize];
    
    return button;
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    [self.aboutUsButton setTitleColor:theme.hintTextColor forState:UIControlStateNormal];
    [self.userAgreementButton setTitleColor:theme.hintTextColor forState:UIControlStateNormal];
    [self.privacyPolicyButton setTitleColor:theme.hintTextColor forState:UIControlStateNormal];
    
    self.separatorView1.backgroundColor = theme.hintTextColor;
    self.separatorView2.backgroundColor = theme.hintTextColor;
}

#pragma mark - Actions

- (void)aboutUsButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapAboutUs)]) {
        [self.delegate bottomToolbarDidTapAboutUs];
    }
}

- (void)userAgreementButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapUserAgreement)]) {
        [self.delegate bottomToolbarDidTapUserAgreement];
    }
}

- (void)privacyPolicyButtonTapped {
    if ([self.delegate respondsToSelector:@selector(bottomToolbarDidTapPrivacyPolicy)]) {
        [self.delegate bottomToolbarDidTapPrivacyPolicy];
    }
}

@end

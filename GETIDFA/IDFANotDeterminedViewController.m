//
//  IDFANotDeterminedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFANotDeterminedViewController.h"
#import "IDFADescriptionView.h"

@interface IDFANotDeterminedViewController ()
@property (nonatomic, strong) UIImageView *guideImageView;
@property (nonatomic, strong) UIButton *authorizeButton;
@property (nonatomic, strong) IDFADescriptionView *descriptionView;
@end

@implementation IDFANotDeterminedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 标题
//    self.titleLabel = [self createTitleLabelWithText:@"获取IDFA权限"];
//    [self.contentView addSubview:self.titleLabel];
//    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.largeSpacing);
//        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
//        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
//    }];

    // IDFA解释说明
    self.descriptionView = [[IDFADescriptionView alloc] init];
    NSArray *descriptions = @[
        @"获取IDFA权限",
        @"个性化广告推荐",
        @"应用使用分析",
        @"跨应用数据统计",
        @"为了更好地为您提供服务，我们需要获取此权限。"
    ];
    [self.descriptionView setupWithDescriptions:descriptions];
    [self.contentView addSubview:self.descriptionView];
    [self.descriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 引导图片（可点击放大）
    self.guideImageView = [self createTappableImageViewWithImageName:@"guide_notdetermined"];
    [self.contentView addSubview:self.guideImageView];
    [self.guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descriptionView.mas_bottom).offset(theme.mediumSpacing);
        make.centerX.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.height.equalTo(@220);
    }];

    // 图片提示文字
    UILabel *imageHintLabel = [self createInfoLabelWithText:@"点击图片可放大查看" fontSize:theme.hintFontSize];
    imageHintLabel.textColor = theme.hintTextColor;
    [self.contentView addSubview:imageHintLabel];
    [imageHintLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.guideImageView.mas_bottom).offset(theme.smallSpacing);
        make.centerX.equalTo(self.contentView);
    }];

    // 授权按钮
    self.authorizeButton = [self createGradientButtonWithTitle:@"🔓 去授权"];
    [self.authorizeButton addTarget:self action:@selector(authorizeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.authorizeButton];
    [self.authorizeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imageHintLabel.mas_bottom).offset(theme.largeSpacing);
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(@200);
        make.height.equalTo(@44);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];

    // 添加按钮点击动画
    [self addButtonAnimation:self.authorizeButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    // 添加按钮点击动画效果
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)authorizeButtonTapped {
    // 调用系统授权弹窗
    [[TrackingStatusManager sharedManager] requestOrCheckTrackingWithCompletion:^(TrackingStatus newStatus) {
        if (self.onStatusChanged) {
            self.onStatusChanged(newStatus);
        }
    }];
}

@end

//
//  IDFANotDeterminedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFANotDeterminedViewController.h"

@interface IDFANotDeterminedViewController ()
@property (nonatomic, strong) UIImageView *guideImageView;
@property (nonatomic, strong) UIButton *authorizeButton;
@property (nonatomic, strong) UILabel *explanationLabel;
@end

@implementation IDFANotDeterminedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // 标题
    self.titleLabel = [self createTitleLabelWithText:@"获取IDFA权限"];
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(50);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
    }];

    // IDFA解释说明
    NSString *explanationText = @"IDFA（广告标识符）是iOS设备的唯一标识符，用于：\n\n• 个性化广告推荐\n• 应用使用分析\n• 跨应用数据统计\n\n为了更好地为您提供服务，我们需要获取此权限。";
    self.explanationLabel = [self createDescriptionLabelWithText:explanationText];
    [self.contentView addSubview:self.explanationLabel];
    [self.explanationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(30);
        make.left.equalTo(self.contentView).offset(25);
        make.right.equalTo(self.contentView).offset(-25);
    }];

    // 引导图片（可点击放大）
    self.guideImageView = [self createTappableImageViewWithImageName:@"guide_notdetermined"];
    [self.contentView addSubview:self.guideImageView];
    [self.guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.explanationLabel.mas_bottom).offset(35);
        make.centerX.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(20);
        make.right.equalTo(self.contentView).offset(-20);
        make.height.equalTo(@280);
    }];

    // 图片提示文字
    UILabel *imageHintLabel = [self createInfoLabelWithText:@"点击图片可放大查看" fontSize:14];
    imageHintLabel.textColor = [UIColor colorWithWhite:0.8 alpha:1.0];
    [self.contentView addSubview:imageHintLabel];
    [imageHintLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.guideImageView.mas_bottom).offset(10);
        make.centerX.equalTo(self.contentView);
    }];

    // 授权按钮
    self.authorizeButton = [self createGradientButtonWithTitle:@"去授权"];
    [self.authorizeButton addTarget:self action:@selector(authorizeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.authorizeButton];
    [self.authorizeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imageHintLabel.mas_bottom).offset(40);
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(@220);
        make.height.equalTo(@55);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];

    // 添加按钮点击动画
    [self addButtonAnimation:self.authorizeButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    // 添加按钮点击动画效果
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)authorizeButtonTapped {
    // 调用系统授权弹窗
    [[TrackingStatusManager sharedManager] requestOrCheckTrackingWithCompletion:^(TrackingStatus newStatus) {
        if (self.onStatusChanged) {
            self.onStatusChanged(newStatus);
        }
    }];
}

@end

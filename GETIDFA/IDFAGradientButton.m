//
//  IDFAGradientButton.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientButton.h"
#import "IDFAThemeManager.h"

@interface IDFAGradientButton ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, assign) IDFAButtonSize buttonSize;
@end

@implementation IDFAGradientButton

- (instancetype)initWithTitle:(NSString *)title {
    return [self initWithTitle:title size:IDFAButtonSizeNormal];
}

- (instancetype)initWithTitle:(NSString *)title size:(IDFAButtonSize)size {
    self = [IDFAGradientButton buttonWithType:UIButtonTypeCustom];
    if (self) {
        _buttonSize = size;
        [self setTitle:title forState:UIControlStateNormal];
        [self setupButton];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupButton {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 根据按钮尺寸设置字体大小和圆角
    CGFloat fontSize = theme.buttonFontSize;
    CGFloat cornerRadius = theme.buttonCornerRadius;

    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            fontSize = theme.buttonFontSize;
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            fontSize = theme.buttonFontSize - 2;
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            fontSize = theme.buttonFontSize - 4;
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 设置文字样式
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:fontSize];

    // 设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    
    // 创建渐变背景
    [self setupGradientBackground];
    
    // 添加边框效果
    self.layer.borderWidth = theme.borderWidth;
    self.layer.borderColor = theme.borderColor.CGColor;
    
    // 添加阴影（需要在父视图中设置masksToBounds = NO）
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOffset = theme.shadowOffset;
    self.layer.shadowRadius = theme.shadowRadius;
    self.layer.shadowOpacity = theme.shadowOpacity;
    
    // 添加触摸动画
    [self addTarget:self action:@selector(touchDown) forControlEvents:UIControlEventTouchDown];
    [self addTarget:self action:@selector(touchUp) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside | UIControlEventTouchCancel];
}

- (void)setupGradientBackground {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 移除旧的渐变层
    if (self.gradientLayer) {
        [self.gradientLayer removeFromSuperlayer];
    }

    // 根据按钮尺寸设置圆角
    CGFloat cornerRadius = theme.buttonCornerRadius;
    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 创建新的渐变层
    self.gradientLayer = [CAGradientLayer layer];

    NSArray *buttonColors = theme.buttonGradientColors;
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in buttonColors) {
        [cgColors addObject:(id)color.CGColor];
    }
    self.gradientLayer.colors = cgColors;
    self.gradientLayer.startPoint = CGPointMake(0, 0);
    self.gradientLayer.endPoint = CGPointMake(1, 1);
    self.gradientLayer.cornerRadius = cornerRadius;

    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    // 更新渐变层的frame
    self.gradientLayer.frame = self.bounds;
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 根据按钮尺寸设置字体大小和圆角
    CGFloat fontSize = theme.buttonFontSize;
    CGFloat cornerRadius = theme.buttonCornerRadius;

    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            fontSize = theme.buttonFontSize;
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            fontSize = theme.buttonFontSize - 2;
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            fontSize = theme.buttonFontSize - 4;
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 更新文字颜色
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:fontSize];

    // 更新圆角
    self.layer.cornerRadius = cornerRadius;
    self.gradientLayer.cornerRadius = cornerRadius;

    // 更新边框
    self.layer.borderWidth = theme.borderWidth;
    self.layer.borderColor = theme.borderColor.CGColor;

    // 更新阴影
    self.layer.shadowOffset = theme.shadowOffset;
    self.layer.shadowRadius = theme.shadowRadius;
    self.layer.shadowOpacity = theme.shadowOpacity;

    // 重新设置渐变背景
    [self setupGradientBackground];
}

#pragma mark - Touch Animation

- (void)touchDown {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformMakeScale(0.95, 0.95);
        self.alpha = 0.8;
    }];
}

- (void)touchUp {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformIdentity;
        self.alpha = 1.0;
    }];
}

#pragma mark - Convenience Methods

+ (CGFloat)recommendedHeightForSize:(IDFAButtonSize)size {
    switch (size) {
        case IDFAButtonSizeNormal:
            return 44.0;
        case IDFAButtonSizeSmall:
            return 36.0;
        case IDFAButtonSizeMini:
            return 28.0;
    }
}

- (CGFloat)recommendedHeight {
    return [IDFAGradientButton recommendedHeightForSize:self.buttonSize];
}

@end

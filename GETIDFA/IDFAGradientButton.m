//
//  IDFAGradientButton.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientButton.h"
#import "IDFAThemeManager.h"

@interface IDFAGradientButton ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@end

@implementation IDFAGradientButton

- (instancetype)initWithTitle:(NSString *)title {
    self = [IDFAGradientButton buttonWithType:UIButtonTypeCustom];
    if (self) {
        [self setTitle:title forState:UIControlStateNormal];
        [self setupButton];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupButton {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 设置文字样式
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:theme.buttonFontSize];
    
    // 设置圆角
    self.layer.cornerRadius = theme.buttonCornerRadius;
    self.layer.masksToBounds = YES;
    
    // 创建渐变背景
    [self setupGradientBackground];
    
    // 添加边框效果
    self.layer.borderWidth = theme.borderWidth;
    self.layer.borderColor = theme.borderColor.CGColor;
    
    // 添加阴影（需要在父视图中设置masksToBounds = NO）
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOffset = theme.shadowOffset;
    self.layer.shadowRadius = theme.shadowRadius;
    self.layer.shadowOpacity = theme.shadowOpacity;
    
    // 添加触摸动画
    [self addTarget:self action:@selector(touchDown) forControlEvents:UIControlEventTouchDown];
    [self addTarget:self action:@selector(touchUp) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside | UIControlEventTouchCancel];
}

- (void)setupGradientBackground {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 移除旧的渐变层
    if (self.gradientLayer) {
        [self.gradientLayer removeFromSuperlayer];
    }
    
    // 创建新的渐变层
    self.gradientLayer = [CAGradientLayer layer];
    
    NSArray *buttonColors = theme.buttonGradientColors;
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in buttonColors) {
        [cgColors addObject:(id)color.CGColor];
    }
    self.gradientLayer.colors = cgColors;
    self.gradientLayer.startPoint = CGPointMake(0, 0);
    self.gradientLayer.endPoint = CGPointMake(1, 1);
    self.gradientLayer.cornerRadius = theme.buttonCornerRadius;
    
    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    // 更新渐变层的frame
    self.gradientLayer.frame = self.bounds;
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 更新文字颜色
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:theme.buttonFontSize];
    
    // 更新圆角
    self.layer.cornerRadius = theme.buttonCornerRadius;
    self.gradientLayer.cornerRadius = theme.buttonCornerRadius;
    
    // 更新边框
    self.layer.borderWidth = theme.borderWidth;
    self.layer.borderColor = theme.borderColor.CGColor;
    
    // 更新阴影
    self.layer.shadowOffset = theme.shadowOffset;
    self.layer.shadowRadius = theme.shadowRadius;
    self.layer.shadowOpacity = theme.shadowOpacity;
    
    // 重新设置渐变背景
    [self setupGradientBackground];
}

#pragma mark - Touch Animation

- (void)touchDown {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformMakeScale(0.95, 0.95);
        self.alpha = 0.8;
    }];
}

- (void)touchUp {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformIdentity;
        self.alpha = 1.0;
    }];
}

@end
